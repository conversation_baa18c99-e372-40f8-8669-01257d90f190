import uuid
from datetime import datetime, timezone

import pytest
from configuration.settings import configuration

from gc_dentist_shared.core.common.hmac import generate_hmac_signature


@pytest.fixture
def valid_headers():
    time_request = str(int(datetime.now(timezone.utc).timestamp() * 1000))
    signature = generate_hmac_signature(
        message=str(time_request), secret=configuration.COMMUNICATE_SECRET_KEY
    )
    return {
        "X-Signature": signature,
        "X-Timestamp": str(time_request),
    }


def get_headers(tenant_uuid, valid_headers=None):
    headers = {"X-Tenant-UUID": tenant_uuid}
    if valid_headers:
        headers.update(valid_headers)
    return headers


def get_clinic_payload(**overrides):
    default_payload = {
        "tenant_uuid": str(uuid.uuid4()),
        "clinic_slug": str(uuid.uuid4()),
        "clinic_name": "Test Clinic",
        "phone_number": "0347913389",
        "email": "<EMAIL>",
        "address_1": "address_1",
        "address_2": "address_2",
        "address_3": "address_3",
        "manager_info": {
            "first_name": "admin",
            "last_name": "test",
            "username": str(uuid.uuid4()).replace("-", "_"),
            "password": str(uuid.uuid4()) + "@A" + "123456",
            "required_change_password": True,
            "is_active": True,
            "phone": "0347913389",
            "country_code": "+81",
            "date_of_birth": "1990-01-01",
            "gender": 1,
        },
    }
    default_payload.update(overrides)
    return default_payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload,headers_func,expected_status",
    [
        (get_clinic_payload(), lambda t, v: get_headers(t), 401),
        (
            {"tenant_uuid": str(uuid.uuid4()), "clinic_name": "Test Clinic"},
            lambda t, v: get_headers(t, v),
            422,
        ),
        (
            get_clinic_payload(email="invalid-email"),
            lambda t, v: get_headers(t, v),
            422,
        ),
        (
            get_clinic_payload(
                manager_info={
                    "username": "test",
                    "password": "123",
                    "required_change_password": True,
                    "is_active": True,
                }
            ),
            lambda t, v: get_headers(t, v),
            422,
        ),
    ],
)
async def test_create_clinic_profile(
    async_client,
    valid_headers,
    tenant_uuid,
    payload,
    headers_func,
    expected_status,
):
    headers = headers_func(tenant_uuid, valid_headers)

    response = await async_client.post(
        "/v1_0/clinic/admin", json=payload, headers=headers
    )
    assert response.status_code == expected_status
