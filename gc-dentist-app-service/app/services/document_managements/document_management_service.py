import re
import uuid
from datetime import datetime, timezone
from typing import Optional

from core.constants import DOCUMENT_MANAGEMENT_DEFAULT_VERSION
from core.messages import CustomMessageCode
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.document_management_schema import (
    DocumentManagementCreateSchema,
    DocumentManagementUpdateSchema,
    DocumentQueryParams,
)
from schemas.responses.document_management_schema import (
    DocumentListSchema,
    DocumentManagementUpdateResponse,
    GetDocumentSchema,
    GetDocumentVersionSchema,
    LibraryListSchema,
)
from sqlalchemy import desc, exists, func, or_, select, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.document import DocumentDataType, DocumentStatus
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import (
    DocumentGroup,
    DocumentManagement,
    MedicalHistory,
    PatientUser,
)


class DocumentManagementService:
    def __init__(self, session: AsyncSession):
        self.session = session

    # region Public Methods
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_document",
    )
    async def get_document(
        self, patient_user_id: int, document_id: int
    ) -> GetDocumentSchema:
        query = select(
            DocumentManagement.id,
            DocumentManagement.patient_user_id,
            DocumentManagement.name,
            DocumentManagement.document_group_id,
            DocumentManagement.data_type,
            DocumentManagement.document_data,
            DocumentManagement.preview_document_data,
            DocumentManagement.examination_date,
            DocumentManagement.medical_history_id,
            DocumentManagement.document_uuid,
            DocumentManagement.display_mode,
            DocumentManagement.document_extension,
            DocumentManagement.s3_status,
            DocumentManagement.version_id,
            DocumentManagement.created_at,
        ).where(
            DocumentManagement.id == document_id,
            DocumentManagement.patient_user_id == patient_user_id,
            DocumentManagement.is_latest.is_(True),
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        )

        async with self.session:
            result = await self.session.execute(query)
            row = result.mappings().one_or_none()

        if not row:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
            )

        return GetDocumentSchema(**row)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_document",
    )
    async def get_list(
        self, patient_user_id: int, filters: Optional[DocumentQueryParams] = None
    ) -> Page[DocumentListSchema]:
        conditions = [
            DocumentManagement.patient_user_id == patient_user_id,
            DocumentManagement.is_latest.is_(True),
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        ]
        conditions_map = {
            "document_group_ids": lambda v: DocumentManagement.document_group_id.in_(v)
        }

        conditions.extend(
            builder(value)
            for key, builder in conditions_map.items()
            if (value := getattr(filters, key)) is not None
        )

        grouped_query = (
            select(
                func.date(DocumentManagement.examination_date).label(
                    "examination_date"
                ),
                func.json_agg(
                    func.json_build_object(
                        "id",
                        DocumentManagement.id,
                        "name",
                        DocumentManagement.name,
                        "patient_user_id",
                        DocumentManagement.patient_user_id,
                        "document_group_id",
                        DocumentManagement.document_group_id,
                        "data_type",
                        DocumentManagement.data_type,
                        "document_data",
                        DocumentManagement.document_data,
                        "preview_document_data",
                        DocumentManagement.preview_document_data,
                        "medical_history_id",
                        DocumentManagement.medical_history_id,
                        "examination_date",
                        DocumentManagement.examination_date,
                        "document_uuid",
                        DocumentManagement.document_uuid,
                        "display_mode",
                        DocumentManagement.display_mode,
                        "created_at",
                        DocumentManagement.created_at,
                        "updated_at",
                        DocumentManagement.updated_at,
                        "document_extension",
                        DocumentManagement.document_extension,
                        "s3_status",
                        DocumentManagement.s3_status,
                        "version_id",
                        DocumentManagement.version_id,
                    )
                ).label("list_document"),
            )
            .where(*conditions)
            .group_by(func.date(DocumentManagement.examination_date))
            .order_by(func.date(DocumentManagement.examination_date).desc())
        )

        result = await paginate(
            self.session,
            grouped_query,
            unique=False,
        )

        for item in result.items:
            item.list_document.sort(
                key=lambda d: ((d.updated_at or d.created_at), d.id),
                reverse=True,
            )

        return result

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_document_management",
    )
    async def create_document_management(self, payload: DocumentManagementCreateSchema):
        async with self.session.begin():
            await self._validate_before_create_document(payload, self.session)

            medical_history_data = await self._get_medical_history_by_date(
                self.session, payload.examination_date, payload.patient_user_id
            )

            build_document_data = payload.model_dump()
            build_document_data.update(
                {
                    "medical_history_id": medical_history_data.get(
                        "medical_history_id"
                    ),
                    "examination_date": medical_history_data.get("examination_date"),
                    "document_data": {
                        d.file_index: d.file_key for d in payload.document_data
                    },
                    "preview_document_data": {
                        d.file_index: d.file_key for d in payload.preview_document_data
                    },
                    "document_uuid": str(uuid.uuid4()),
                    "version_id": DOCUMENT_MANAGEMENT_DEFAULT_VERSION,
                }
            )

            # Create Document Management
            document_management = DocumentManagement(**build_document_data)
            self.session.add(document_management)
            await self.session.flush()
            await self.session.refresh(document_management)

            return document_management.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_document_management",
    )
    async def update_document_management(
        self, document_management_id: int, payload: DocumentManagementUpdateSchema
    ) -> DocumentManagementUpdateResponse:
        async with self.session.begin():
            document_management = await self._validate_before_update_document(
                self.session, document_management_id, payload.name
            )

            if not document_management:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                    message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
                )

            # Check if conditions are met to keep data_type as EDITED, otherwise remove it
            should_keep_edited = (
                document_management.data_type == DocumentDataType.ORIGINAL.value
                and payload.data_type == DocumentDataType.EDITED.value
                and payload.document_data
            )

            update_fields = {}

            if should_keep_edited:
                update_fields["data_type"] = DocumentDataType.EDITED.value
            else:
                # Remove data_type from payload if conditions are not met
                delattr(payload, "data_type")

            # If examination_date exists, get medical_history information and update update_fields
            if payload.examination_date:
                medical_history_data = await self._get_medical_history_by_date(
                    db_session=self.session,
                    examination_date=payload.examination_date,
                    patient_user_id=payload.patient_user_id,
                )

                update_fields.update(medical_history_data)

                # Handle document update or version creation based on override_flag

            if payload.override_flag:
                # Only update existing document when override_flag=True
                self._update_document_fields(
                    document_management, payload, update_fields
                )
                return DocumentManagementUpdateResponse.model_validate(
                    document_management
                )

            # Create new version: DO NOT update existing record, only create new version with new data
            document_management = await self._create_new_version(
                self.session, document_management, payload, update_fields
            )

            return DocumentManagementUpdateResponse.model_validate(document_management)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="delete_document_management",
    )
    async def delete_document_management(self, document_management_id: int):
        async with self.session.begin():
            document_management = await self.get_document_by_id(
                self.session, document_management_id
            )

            if not document_management:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                    message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
                )

            await self.session.execute(
                update(DocumentManagement)
                .where(DocumentManagement.id == document_management_id)
                .values(
                    status=DocumentStatus.INACTIVATED.value,
                    deleted_at=datetime.now(timezone.utc),
                )
            )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_libraries",
    )
    async def get_libraries(self, patient_user_id: int) -> Page[LibraryListSchema]:
        latest_docs = (
            select(DocumentManagement)
            .where(
                DocumentManagement.patient_user_id == patient_user_id,
                DocumentManagement.status == DocumentStatus.ACTIVATED.value,
                DocumentManagement.deleted_at.is_(None),
                DocumentManagement.is_latest.is_(True),
            )
            .subquery()
        )

        d = aliased(DocumentManagement)

        query = select(
            latest_docs.c.id.label("document_id"),
            latest_docs.c.patient_user_id,
            latest_docs.c.document_uuid,
            latest_docs.c.document_data,
            latest_docs.c.preview_document_data,
            latest_docs.c.data_type,
            latest_docs.c.display_mode,
            latest_docs.c.document_uuid,
            latest_docs.c.version_id,
            latest_docs.c.s3_status,
            latest_docs.c.is_latest,
            latest_docs.c.created_at,
            (
                select(func.json_agg(d.version_id))
                .where(d.document_uuid == latest_docs.c.document_uuid)
                .where(d.is_latest.is_(False))
                .scalar_subquery()
            ).label("history_version_ids"),
        ).order_by(desc(latest_docs.c.created_at))

        return await paginate(
            self.session,
            query,
            unique=False,
        )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_document_version",
    )
    async def get_document_version(
        self, patient_user_id: int, document_uuid: str, version_id: int
    ) -> GetDocumentVersionSchema:
        conditions = [
            DocumentManagement.patient_user_id == patient_user_id,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
            DocumentManagement.deleted_at.is_(None),
            DocumentManagement.document_uuid == document_uuid,
            DocumentManagement.version_id == version_id,
        ]

        query = select(
            DocumentManagement.document_uuid,
            DocumentManagement.patient_user_id,
            DocumentManagement.version_id,
            DocumentManagement.document_data,
            DocumentManagement.preview_document_data,
            DocumentManagement.data_type,
            DocumentManagement.display_mode,
            DocumentManagement.s3_status,
            DocumentManagement.is_latest,
            DocumentManagement.created_at,
            DocumentManagement.created_by,
            DocumentManagement.updated_by,
        ).where(*conditions)

        async with self.session:
            result = await self.session.execute(query)
            row = result.mappings().one_or_none()

        if not row:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
            )

        return GetDocumentVersionSchema(**row)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_document_by_id",
    )
    async def get_document_by_id(
        self, db_session: AsyncSession, document_management_id: int
    ) -> DocumentManagement:
        stmt = select(DocumentManagement).where(
            DocumentManagement.id == document_management_id,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    # endregion Public Methods

    # region Private Methods
    async def _validate_before_create_document(self, obj, db_session: AsyncSession):
        # Validate patient user
        stmt_patient = select(PatientUser).where(
            PatientUser.id == obj.patient_user_id, PatientUser.status.is_(True)
        )
        result_patient = await db_session.execute(stmt_patient)
        patient_user = result_patient.scalar_one_or_none()
        if not patient_user:
            raise CustomValueError(
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
            )

        # Validate document group
        stmt_document_group = select(DocumentGroup).where(
            DocumentGroup.id == obj.document_group_id,
            DocumentGroup.deleted_at.is_(None),
        )
        result_document_group = await db_session.execute(stmt_document_group)
        document_group = result_document_group.scalar_one_or_none()
        if not document_group:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
            )

        # Validate document name
        obj.name = await self._check_and_get_document_name(db_session, obj.name)

    async def _check_and_get_document_name(
        self, db_session: AsyncSession, document_name: str
    ) -> str:
        pattern = rf"^{re.escape(document_name)} \((\d+)\)$"

        query = select(DocumentManagement.name).where(
            or_(
                DocumentManagement.name == document_name,
                DocumentManagement.name.like(f"{document_name} (%)"),
            ),
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        )

        result = await db_session.execute(query)
        existing_names = {row[0] for row in result.fetchall()}

        if document_name not in existing_names:
            return document_name

        regex = re.compile(pattern)
        max_number = 0

        for name in existing_names:
            if name != document_name:
                match = regex.match(name)
                if match:
                    try:
                        number = int(match.group(1))
                        max_number = max(max_number, number)
                    except ValueError:
                        continue  # Ignore invalid numbers

        return f"{document_name} ({max_number + 1})"

    async def _get_medical_history_by_date(
        self,
        db_session: AsyncSession,
        examination_date: Optional[str],
        patient_user_id: int,
    ):
        conditions = [MedicalHistory.patient_user_id == patient_user_id]
        exam_date = None

        if examination_date:
            exam_date = datetime.strptime(examination_date, "%Y-%m-%d").date()
            conditions.append(
                func.date(MedicalHistory.visit_start_datetime) == exam_date
            )

        stmt = (
            select(MedicalHistory)
            .where(*conditions)
            .order_by(MedicalHistory.visit_start_datetime.desc())
        )
        result = await db_session.execute(stmt)
        medical_history = result.scalar_one_or_none()

        final_exam_date = exam_date or (
            medical_history.visit_start_datetime.date() if medical_history else None
        )

        return {
            "medical_history_id": getattr(medical_history, "id", None),
            "examination_date": final_exam_date,
        }

    async def _validate_before_update_document(
        self,
        db_session: AsyncSession,
        document_management_id: int,
        document_name: str = None,
    ):
        await self._check_document_name_exists(db_session, document_name)

        return await self.get_document_by_id(db_session, document_management_id)

    async def _check_document_name_exists(
        self,
        db_session: AsyncSession,
        document_name: str,
    ):
        query = exists(DocumentManagement.id).where(
            DocumentManagement.name == document_name,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        )

        result = await db_session.execute(select(query))
        existing_document = result.scalar()

        if existing_document:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NAME_EXISTS.code,
                message=CustomMessageCode.DOCUMENT_MANAGEMENT_NAME_EXISTS.title,
            )

    def _update_document_fields(self, document_management, update_obj, update_fields):
        update_data = update_obj.model_dump(exclude_unset=True)
        update_data.update(update_fields)

        for field in ["document_data", "preview_document_data"]:
            if field not in update_data or update_data.get(field) is None:
                continue
            new_dict = {}
            for item in update_data.get(field):
                new_dict[item.get("file_index")] = item.get("file_key")
            update_data[field] = new_dict

        for field, value in update_data.items():
            if hasattr(document_management, field):
                setattr(document_management, field, value)

    def _prepare_document_data(self, new_data, existing_data):
        """
        Helper to prepare document data for update.
        If new_data is provided, use it. Otherwise, keep existing_data.
        """
        if new_data:
            return {item.file_index: item.file_key for item in new_data}
        return existing_data

    async def _get_next_version_id(self, document_uuid):
        """Get the next available version_id for a given document_uuid by querying the database"""
        # Query the database to get the maximum version_id for this document_uuid
        max_version_query = await self.session.execute(
            select(func.max(DocumentManagement.version_id)).where(
                DocumentManagement.document_uuid == document_uuid,
                DocumentManagement.status == DocumentStatus.ACTIVATED.value,
            )
        )
        max_version = max_version_query.scalar()

        return (max_version or 0) + 1

    async def _create_new_version(
        self, db_session: AsyncSession, document_management, payload, update_fields
    ):
        """Create a new version of the document"""
        version_id = await self._get_next_version_id(document_management.document_uuid)

        # Mark all existing versions as not latest
        await db_session.execute(
            update(DocumentManagement)
            .where(
                DocumentManagement.document_uuid == document_management.document_uuid
            )
            .values(is_latest=False)
        )

        # Create a copy of document_management (DO NOT modify existing record)
        data = document_management.__dict__.copy()
        data.pop("_sa_instance_state", None)
        data.pop("id", None)

        # Create new version with data from existing document + payload + update_fields
        new_version_data = {
            **data,
            "version_id": version_id,
            "is_latest": True,
        }

        payload_data = payload.model_dump(exclude_unset=True)
        payload_data.update(update_fields)
        payload_data.pop("override_flag", None)

        new_version_data.update(payload_data)

        new_version = DocumentManagement(**new_version_data)
        db_session.add(new_version)
        await db_session.flush()
        await db_session.refresh(new_version)

        return new_version

    # endregion Private Methods # noqa: E305
