from api.health_check import router as health_check_router
from api.v1.authz.auth_api import router as auth_router
from api.v1.authz.auth_s3_api import router as auth_s3_router
from api.v1.clinic.clinic_api import router as clinic_router
from api.v1.doctors.doctor_api import router as doctor_api
from api.v1.document_group.document_group_api import router as document_group_router
from api.v1.document_managements.document_management_api import (
    router as document_management_router,
)
from api.v1.form_flows.form_flow_api import router as form_router
from api.v1.form_flows.form_submission_api import router as form_submission_router
from api.v1.master.master_api import router as master_router
from api.v1.medical_history.medical_history_api import router as medical_history_router
from api.v1.medical_template.medical_template_api import (
    router as medical_template_router,
)
from api.v1.oral_examination.intraoral_examination_api import (
    router as intraoral_examination_api,
)
from api.v1.oral_examination.oral_examination_api import (
    router as oral_examination_router,
)
from api.v1.patients.patient_relationship_api import (
    router as patient_relationship_router,
)
from api.v1.patients.patient_waiting_api import router as patient_waiting_router
from api.v1.patients.patients_api import router as patients_router
from api.v1.permissions.permison_api import router as permission_router
from api.v1.reservations.reservation_api import router as reservation_router
from api.v1.restore_s3_data.restore_s3_data_api import router as restore_s3_data_router

# from fastapi import APIRouter
from core.api_version_router import VersionedAPIRouter

router = VersionedAPIRouter()

router.include_router(auth_router, prefix="/auth", tags=["auth"])
router.include_router(auth_s3_router, prefix="/auth-s3", tags=["auth-s3"])
router.include_router(clinic_router, prefix="/clinic", tags=["clinic"])
router.include_router(master_router, prefix="/master-data", tags=["master-data"])
router.include_router(
    medical_history_router, prefix="/medical-history", tags=["Medical History"]
)
router.include_router(reservation_router, prefix="/reservations", tags=["reservation"])
router.include_router(
    intraoral_examination_api,
    prefix="/intraoral-examinations",
    tags=["Oral Examinations"],
)
router.include_router(
    oral_examination_router, prefix="/oral-examinations", tags=["Oral Examinations"]
)
router.include_router(health_check_router)
router.include_router(
    form_router, prefix="/form-flows", tags=["Forms Flows Management"]
)
router.include_router(patients_router, prefix="/patients", tags=["Patients"])
router.include_router(
    patient_waiting_router, prefix="/patient-waitings", tags=["Patient Waitings"]
)
router.include_router(doctor_api, prefix="/doctors", tags=["Doctors"])

router.include_router(
    form_submission_router,
    prefix="/form-submissions",
    tags=["Forms Submission Management"],
)
router.include_router(
    medical_template_router,
    prefix="/medical-templates",
    tags=["Medical Templates"],
)

router.include_router(
    document_management_router,
    prefix="/document-managements",
    tags=["Document Management"],
)

router.include_router(
    patient_relationship_router,
    prefix="/patient-relationships",
    tags=["Patient Relationships"],
)
router.include_router(restore_s3_data_router, prefix="/s3", tags=["Restore S3 Data"])

router.include_router(permission_router, prefix="/permissions", tags=["Permissions"])

router.include_router(
    document_group_router,
    prefix="/document-groups",
    tags=["Document Groups"],
)
