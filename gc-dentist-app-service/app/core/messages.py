from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    INVALID_REQUEST_PAYLOAD = (
        4422,
        "Invalid request payload!",
        "The request payload is invalid or improperly formatted",
    )

    FORBIDDEN_ERROR = (
        4403,
        "Permission Denied!",
        "You do not have the necessary permissions to perform this action or access this resource.",
    )

    CALL_OPA_ERROR = (
        4500,
        "Authorization Service Error",
        "The system failed to verify permissions with the authorization service. Please try again later.",
    )

    # Server Errors
    UNKNOWN_ERROR = (
        5000,
        _("Unknown error!", "「不明なエラーです!"),
        "An unexpected error occurred.",
    )
    TWILIO_ERROR = (
        5001,
        "Twilio error!",
        "An error occurred while processing the Twilio request.",
    )
    TWILIO_SEND_MESSAGE_ERROR = (
        5002,
        "Twilio send message error!",
        "An error occurred while sending a message via Twilio.",
    )
    S3_BUCKET_ERROR = (
        5003,
        "S3 bucket error!",
        "An error occurred while interacting with the S3 bucket.",
    )
    S3_BUCKET_KEY_NOT_FOUND = (
        5004,
        "S3 bucket key not found!",
        "The specified key does not exist in the S3 bucket.",
    )
    REDIS_KEY_CREATION_FAILED = (
        5005,
        "Redis key creation failed!",
        "An error occurred while creating the Redis key.",
    )

    # Tenant Clinic
    X_CLINIC_NO_IS_REQUIRED = (
        4000,
        _(
            "X-Tenant-Clinic-No header is required!",
            "X-Tenant-Clinic-No ヘッダーは必須です。",
        ),
        "The X-Tenant-Clinic-No header must be provided in the request.",
    )
    TENANT_NOT_FOUND = (
        4004,
        _("Tenant not found!", "テナントが見つかりません。"),
        "The specified tenant does not exist.",
    )

    CLINIC_INFO_NOT_FOUND = (
        4005,
        _("Clinic information not found!", "クリニック情報が見つかりません。"),
        "The specified clinic information does not exist.",
    )
    CLINIC_CREATED_SUCCESS = (
        4006,
        _("Clinic created successfully!", "クリニックが正常に作成されました。"),
        "The clinic has been created successfully.",
    )
    CLINIC_CREATED_FAILED = (
        4007,
        _("Clinic creation failed!", "クリニックの作成に失敗しました。"),
        "An error occurred while creating the clinic.",
    )
    CLINIC_NOT_FOUND = (
        4008,
        _("Clinic not found!", "クリニックが見つかりません。"),
        "The specified clinic does not exist.",
    )
    CLINIC_CONFIGURATION_NOT_FOUND = (
        4009,
        "Clinic configuration not found!",
        "The clinic configuration does not exist.",
    )
    CLINIC_CONFIGURATION_GET_FAILED = (
        4010,
        "Clinic configuration get failed!",
        "An error occurred while getting the clinic configuration.",
    )

    # ValueError
    VALUE_ERROR_INVALID_WHOLE_TOOTH_OR_POINT = (
        10001,
        "Only one of whole_tooth or points should be set!",
        "The request must specify either whole_tooth or points, but not both.",
    )
    VALUE_ERROR_INVALID_DATE_PAST = (
        10002,
        "This date cannot be in the past!",
        "The date provided cannot be earlier than today.",
    )
    VALUE_ERROR_AT_LEAST_ONE_FIELD = (
        10003,
        "At least one field must be provided!",
        "At least one field must be provided for the update operation.",
    )
    VALUE_ERROR_INVALID_DATE_STRING_TYPE_FORMAT = (
        10004,
        "Invalid date string format!",
        "The date string must be a string in the format 'YYYY-MM-DD' or 'YYYY/MM/DD'.",
    )
    VALUE_ERROR_INVALID_DATETIME_TYPE = (
        10005,
        "Invalid datetime type!",
        "The datetime must be a string in the format datetime.datetime not datetime.date or datetime.time.",
    )
    VALUE_ERROR_INVALID_ROLE = (
        10006,
        "Invalid role!",
        "The specified role is not valid or does not exist.",
    )
    VALUE_ERROR_INVALID_FILE_NAMES = (
        10007,
        "Invalid file names!",
        "The file names provided are invalid or empty.",
    )
    VALUE_ERROR_INVALID_PHONE_NUMBER_FORMAT = (
        10008,
        "Invalid phone number format!",
        "The phone number format is invalid.",
    )
    VALUE_ERROR_INVALID_OPTION = (
        10009,
        "Invalid option. Please select a valid value.",
        "The provided value is not among the allowed options.",
    )

    # Role
    ROLE_CREATED_SUCCESS = (
        20001,
        _("Role created successfully!", "ロールが正常に作成されました。"),
        "The role has been created successfully.",
    )
    ROLE_CREATED_FAILED = (
        20002,
        _("Role creation failed!", "ロールの作成に失敗しました。"),
        "An error occurred while creating the role.",
    )

    # Master Data
    MASTER_DATA_NOT_FOUND = (
        30001,
        _("Master data not found!", "マスターデータが見つかりません。"),
        "The requested master data does not exist.",
    )
    MASTER_DATA_FETCH_SUCCESS = (
        30002,
        _(
            "Master data fetched successfully!",
            "マスターデータが正常に取得されました。",
        ),
        "The master data has been retrieved successfully.",
    )
    MASTER_DATA_FETCH_FAILED = (
        30003,
        _("Master data fetch failed!", "マスターデータの取得に失敗しました。"),
        "An error occurred while fetching the master data.",
    )
    MASTER_DATA_COLUMN_NOT_EXIST = (
        30004,
        _(
            "Master data column does not exist!",
            "マスターデータに指定されたカラムが存在しません。",
        ),
        "The specified column does not exist in the master data model.",
    )
    MASTER_DATA_INVALID_FILTER = (
        30005,
        _("Invalid filter condition!", "無効なフィルター条件です。"),
        "The provided filter condition is invalid or malformed.",
    )

    # User
    LOGIN_SUCCESS = (
        40000,
        _("Login successful!", "ログインに成功しました。"),
        "User has successfully logged in.",
    )
    USER_NOT_FOUND = (
        40001,
        _("User not found!", "ユーザーが見つかりません。"),
        "The specified user does not exist.",
    )
    INVALID_USERNAME_OR_PASSWORD = (
        40002,
        _("Invalid username or password!", "ユーザー名またはパスワードが無効です。"),
        "The provided username or password is incorrect.",
    )

    # Form flow management
    FORM_FLOW_ERROR_NAME_EXISTS = (
        500001,
        "Form flow name already exists!",
        "A form flow with the given name already exists in the system.",
    )
    FORM_FLOW_ERROR_MINIMUM_FORMS = (
        500002,
        "At least one form is required!",
        "You must define at least one form within a form flow.",
    )
    FORM_FLOW_ERROR_EMPTY_CONTENT_ORDER = (
        500003,
        "Form cannot be empty!",
        "A form must contain at least one item or one group.",
    )
    FORM_FLOW_ERROR_EMPTY_GROUP_ITEMS = (
        500004,
        "Form group must have items!",
        "Each group within a form must contain at least one item.",
    )
    FORM_FLOW_ERROR_DUPLICATE_FORM_ORDER = (
        500005,
        "Duplicate form order index!",
        "Each form within the flow must have a unique order_index.",
    )
    FORM_FLOW_ERROR_DUPLICATE_FORM_CONTENT_ORDER = (
        500006,
        "Duplicate form content order index!",
        "The order_index of items and groups within the same form must be unique.",
    )
    FORM_FLOW_ERROR_DUPLICATE_GROUP_ITEM_ORDER = (
        500007,
        "Duplicate group item order index!",
        "The order_index of items within the same group must be unique.",
    )
    FORM_FLOW_ERROR_CREATE_FAILED = (
        500008,
        "Failed to create form flow!",
        "An unexpected error occurred while creating the form flow.",
    )

    FORM_FLOW_ERROR_DETAIL_FAILED = (
        500009,
        "Failed to get detail form flow!",
        "An unexpected error occurred while get detail the form flow.",
    )
    FORM_FLOW_ERROR_NOT_FOUND = (
        500404,
        "Form flow not found!",
        "The requested form flow could not be found.",
    )
    FORM_FLOW_ERROR_FORM_NOT_DELETABLE = (
        500010,
        "Form is not deletable! (UUID: {uuid})",
        "The form cannot be deleted, it might be in use.",
    )
    FORM_FLOW_ERROR_GROUP_NOT_DELETABLE = (
        500011,
        "Group is not deletable! (UUID: {uuid})",
        "The group cannot be deleted, it might be in use.",
    )
    FORM_FLOW_ERROR_ITEM_NOT_DELETABLE = (
        500012,
        "Item is not deletable! (UUID: {uuid})",
        "The item cannot be deleted, it might be in use.",
    )
    FORM_FLOW_ERROR_FORM_NOT_FOUND = (
        500013,
        "Form not found! (UUID: {uuid})",
        "The requested form could not be found within the form flow.",
    )
    FORM_FLOW_ERROR_GROUP_NOT_FOUND = (
        500014,
        "Group not found! (UUID: {uuid})",
        "The requested group could not be found within the form.",
    )
    FORM_FLOW_ERROR_ITEM_NOT_FOUND = (
        500015,
        "Item not found! (UUID: {uuid})",
        "The requested item could not be found within the group or form.",
    )
    FORM_FLOW_ERROR_EDIT_FAILED = (
        500016,
        "Failed to edit form flow!",
        "An unexpected error occurred while editing the form flow.",
    )
    FORM_FLOW_ERROR_UUID_REQUIRED = (
        500017,
        "UUID is required!",
        "A UUID must be provided for UPDATE or DELETE actions.",
    )
    FORM_FLOW_ERROR_UUID_INVALID = (
        500018,
        "Invalid UUID format!",
        "The provided UUID is not valid. Please ensure it follows the standard UUID format.",
    )
    FORM_FLOW_ERROR_EMPTY_FORM = (
        500019,
        "Form Flow cannot be empty!",
        "A form flow must contain at least one form",
    )
    FORM_FLOW_ERROR_UUID_SHOULD_NOT_EXIST_ON_CREATE = (
        500020,
        "UUID should not be provided when creating.",
        "When creating a new resource, the UUID must not be included as it will be generated by the system.",
    )
    FORM_FLOW_ERROR_GET_LIST_FAILED = (
        500026,
        "Failed to get list form flow!",
        "An unexpected error occurred while get list the form flow.",
    )
    FORM_FLOW_EDIT_SUCCESS = (
        500027,
        "Edit form flow successfully.",
        "The form flow was edited successfully.",
    )

    FORM_FLOW_CREATE_SUCCESS = (
        500028,
        "Create form flow successfully.",
        "A new form flow was successfully created.",
    )

    FORM_FLOW_GET_DETAIL_SUCCESS = (
        500029,
        "Get form flow detail successfully.",
        "The details of the form flow were retrieved successfully.",
    )

    FORM_FLOW_DELETE_SUCCESS = (
        500030,
        "Deleted form flow successfully.",
        "The form flow was Deleted successfully.",
    )

    FORM_FLOW_ERROR_DELETE_FAILED = (
        500031,
        "Failed to deleted form flow!",
        "An unexpected error occurred while deleted the form flow.",
    )

    FORM_FLOW_ERROR_FORM_FLOW_NOT_DELETABLE = (
        500032,
        "Form flow is not deletable!",
        "The Form form cannot be deleted, it might be in use.",
    )

    FORM_FLOW_ERROR_FORM_CONFLICT = (
        500035,
        "Form UUID conflict (UUID: {uuid})",
        "A form with the same UUID already exists in the current form flow.",
    )

    FORM_FLOW_ERROR_GROUP_CONFLICT = (
        500036,
        "Group UUID conflict (UUID: {uuid})",
        "A group with the same UUID already exists in the specified form.",
    )

    FORM_FLOW_ERROR_ITEM_CONFLICT = (
        500037,
        "Item UUID conflict (UUID: {uuid})",
        "An item with the same UUID already exists in the specified group or form.",
    )

    # Form Submission
    FORM_SUBMISSION_CREATE_SUCCESS = (
        500100,
        "Form submitted successfully!",
        "The form submission has been successfully created and stored.",
    )
    FORM_SUBMISSION_ERROR_CREATE_FAILED = (
        500101,
        "Failed to create form submission.",
        "An unexpected error occurred while trying to save the form submission.",
    )
    FORM_SUBMISSION_ERROR_INVALID_FLOW = (
        500102,
        "Invalid Form Flow for submission.",
        "The provided form flow UUID does not exist or is inactive.",
    )
    FORM_SUBMISSION_ERROR_FORM_NOT_FOUND = (
        500103,
        "Submitted form not found. (UUID: {uuid})",
        "A form UUID provided in the submission payload is invalid or does not belong to the specified form flow.",
    )
    FORM_SUBMISSION_ERROR_GROUP_NOT_FOUND = (
        500104,
        "Submitted group not found. (UUID: {uuid})",
        "A group UUID provided in the submission is invalid or does not belong to its parent form.",
    )
    FORM_SUBMISSION_ERROR_ALREADY_EXISTS = (
        500105,
        "Submission already exists for this patient and form.",
        "This patient has already submitted this specific form flow, and duplicates are not allowed.",
    )
    FORM_SUBMISSION_ERROR_ITEM_NOT_FOUND = (
        500106,
        "Submitted item not found. (UUID: {uuid})",
        "An item UUID provided in the submission payload does not exist in the database.",
    )
    FORM_SUBMISSION_ERROR_UUID_MISMATCH = (
        500107,
        "Form Flow UUID mismatch.",
        "The top-level form_flow_uuid does not match the UUID inside the form_flow_data object.",
    )

    FORM_SUBMISSION_GET_DETAIL_SUCCESS = (
        500110,
        "Get form submission detail successfully!",
        "Successfully retrieved the details of the form submission.",
    )
    FORM_SUBMISSION_ERROR_NOT_FOUND = (
        500111,
        "Form submission not found.",
        "The requested form submission does not exist or is inactive.",
    )
    FORM_SUBMISSION_ERROR_GET_DETAIL_FAILED = (
        500112,
        "Failed to get form submission detail.",
        "An unexpected error occurred while trying to retrieve the form submission detail.",
    )

    FORM_SUBMISSION_ERROR_DELETE_FAILED = (
        500121,
        "Failed to delete form submission.",
        "An unexpected error occurred while trying to delete the form submission.",
    )

    FORM_SUBMISSION_ERROR_GET_LIST_FAILED = (
        500130,
        "Failed to get form submissions list.",
        "An unexpected error occurred while trying to retrieve the form submissions list.",
    )

    FORM_SUBMISSION_UPDATE_SUCCESS = (
        500140,
        "Form submission updated successfully!",
        "The answers in the form submission have been successfully updated.",
    )
    FORM_SUBMISSION_ERROR_UPDATE_FAILED = (
        500141,
        "Failed to update form submission.",
        "An unexpected error occurred while trying to update the form submission.",
    )
    FORM_SUBMISSION_ERROR_ITEM_NOT_IN_SUBMISSION = (
        500142,
        "Item not found in this submission.",
        "An item UUID provided does not belong to the specified form submission.",
    )

    # Patient
    PATIENT_CLINICAL_EXISTS = (
        60001,
        _("Patient clinical number exists!", "患者のカルテ番号が既に存在します。"),
        "The specified patient clinical number already exists.",
    )
    PATIENT_CREATED_FAILED = (
        60002,
        _("Patient creation failed!", "患者の作成に失敗しました。"),
        "An error occurred while creating the patient.",
    )
    PATIENT_NOT_FOUND = (
        60003,
        _("Patient not found!", "患者が見つかりません。"),
        "The specified patient does not exist.",
    )
    PATIENT_UPDATED_FAILED = (
        60004,
        _("Patient update failed!", "患者の更新に失敗しました。"),
        "An error occurred while updating the patient.",
    )
    PATIENT_DELETED_FAILED = (
        60005,
        _("Patient deletion failed!", "患者の削除に失敗しました。"),
        "An error occurred while deleting the patient.",
    )
    PATIENT_DELETED_SUCCESS = (
        60006,
        _("Patient deleted successfully!", "患者が正常に削除されました。"),
        "The patient has been deleted successfully.",
    )
    PATIENT_NO_GENERATION_FAILED = (
        60007,
        _(
            "Patient clinical number generation failed!",
            "患者のカルテ番号の生成に失敗しました。",
        ),
        "An error occurred while generating the patient clinical number.",
    )
    PATIENT_GET_LIST_FAILED = (
        60008,
        _("Patient get list failed!", "患者一覧の取得に失敗しました。"),
        "An error occurred while get list the patient.",
    )
    PATIENT_DETAIL_FAILED = (
        60009,
        _("Patient get detail failed!", "患者詳細の取得に失敗しました。"),
        "An error occurred while get detail the patient.",
    )
    PATIENT_PHONE_MISSING = (
        60010,
        _("Patient phone missing!", "患者の電話番号が登録されていません。"),
        "The patient does not have a registered phone number.",
    )
    PATIENT_PHONE_EXISTS = (
        60011,
        _("Patient phone number already exists!", "患者の電話番号が既に存在します。"),
        "The specified phone number already exists for another patient.",
    )

    # Patient Waiting
    PATIENT_WAITING_CREATED_SUCCESS = (
        70001,
        _(
            "Patient waiting created successfully!",
            "患者の待機が正常に作成されました。",
        ),
        "The patient waiting record has been created successfully.",
    )
    PATIENT_USER_NOT_FOUND = (
        70002,
        _("Patient user not found!", "患者ユーザーが見つかりません。"),
        "The specified patient user does not exist.",
    )
    DOCTOR_NOT_FOUND = (
        70003,
        _("Doctor not found!", "医師が見つかりません。"),
        "The specified doctor does not exist.",
    )
    PATIENT_WAITING_RESERVATION_CREATION_FAILED = (
        70004,
        _(
            "Patient waiting reservation creation failed!",
            "患者待機予約の作成に失敗しました。",
        ),
        "An error occurred while creating the reservation for the patient waiting record.",
    )
    PATIENT_WAITING_RESERVATION_NOT_FOUND = (
        70005,
        _("Patient waiting reservation not found!", "患者待機予約が見つかりません。"),
        "The specified reservation for the patient waiting record does not exist.",
    )
    PATIENT_WAITING_GET_LIST_FAILED = (
        70006,
        _("Patient waiting get list failed!", "患者待機一覧の取得に失敗しました。"),
        "An error occurred while get list the patient waiting.",
    )
    PATIENT_WAITING_NOT_FOUND = (
        70007,
        _("Patient waiting not found!", "患者待機が見つかりません。"),
        "The specified patient waiting record does not exist.",
    )

    # Medical History
    MEDICAL_HISTORY_NOT_FOUND = (
        80001,
        "Medical history not found!",
        "The specified medical history does not exist.",
    )

    # Oral Examination
    ORAL_EXAMINATION_NOT_FOUND = (
        90001,
        "Oral examination not found!",
        "The specified oral examination does not exist.",
    )

    # Doctor
    DOCTOR_CREATED_FAILED = (
        100001,
        _("Doctor creation failed!", "ドクターの作成に失敗しました。"),
        "An error occurred while creating the doctor.",
    )
    DOCTOR_EXISTS_EMAIL = (
        100002,
        _("Email already exists!", "メールアドレスは既に存在します。"),
        "An error occurred while creating the doctor.",
    )
    DOCTOR_EXISTS_PHONE = (
        100003,
        _("Phone already exists!", "電話番号は既に存在します。"),
        "An error occurred while creating the doctor.",
    )
    DOCTOR_CREATED_SUCCESS = (
        100004,
        _("Doctor created successfully!", "ドクターが正常に作成されました。"),
        "The doctor has been created successfully.",
    )
    DOCTOR_DETAIL_FAILED = (
        100005,
        _(
            "Doctor profile retrieval failed!",
            "ドクタープロフィールの取得に失敗しました。",
        ),
        "An error occurred while get the doctor.",
    )
    DOCTOR_UPDATED_FAILED = (
        100006,
        _("Doctor update failed!", "ドクターの更新に失敗しました。"),
        "An error occurred while updating the doctor.",
    )
    DOCTOR_UPDATED_SUCCESS = (
        100007,
        _("Doctor updated successfully!", "ドクターが正常に更新されました。"),
        "The doctor has been updated successfully.",
    )
    DOCTOR_GET_LIST_FAILED = (
        100008,
        _("Doctor list retrieval failed!", "ドクター一覧の取得に失敗しました。"),
        "An error occurred while get list the doctor.",
    )
    DOCTOR_ADMIN_ROLE_REQUIRED = (
        100009,
        _("Admin role required!", "管理者権限が必要です。"),
        "The doctor does not have admin role to perform this action.",
    )

    # Reservation
    RESERVATION_GET_LIST_FAILED = (
        110001,
        _(
            "Get list and save reservation failed!",
            "予約一覧の取得と保存に失敗しました。",
        ),
        "An error occurred while get list and save reservation.",
    )
    RESERVATION_SYNC_TO_PATIENT_WAITING = (
        110002,
        _(
            "Sync data to patient waiting failed!",
            "予約データを患者待機リストに同期できませんでした。",
        ),
        "An error occurred while sync data from reservation.",
    )
    RESERVATION_GET_LIST_ERROR_VALUE = (
        110003,
        _(
            "Get and save reservation records with errors!",
            "エラー付きの予約記録の取得と保存に失敗しました。",
        ),
        "An error occurred while get list and save reservation.",
    )

    # Clinic source
    CLINIC_SOURCE_MAPPING_NOT_FOUND = (
        120001,
        "Not found clinic source mapping",
        "An error occurred while info clinic source mapping.",
    )

    # Medical Template
    MEDICAL_TEMPLATE_CREATED_FAILED = (
        130001,
        "Failed to create medical template!",
        "An error occurred while creating the medical template.",
    )
    MEDICAL_TEMPLATE_NOT_FOUND = (
        130002,
        "Medical template not found!",
        "The specified medical template does not exist.",
    )
    DOCUMENT_GROUP_NOT_FOUND = (
        130003,
        "Document group not found!",
        "The specified document group does not exist.",
    )
    MEDICAL_TEMPLATE_GET_FAILED = (
        130004,
        "Failed to get medical template!",
        "An error occurred while getting the medical template.",
    )

    MEDICAL_TEMPLATE_UPDATED_FAILED = (
        130005,
        "Failed to update medical template!",
        "An error occurred while updating the medical template.",
    )
    MEDICAL_TEMPLATE_DELETED_FAILED = (
        130006,
        "Failed to delete medical template!",
        "An error occurred while deleting the medical template.",
    )
    MEDICAL_TEMPLATE_GET_LIST_FAILED = (
        130007,
        "Failed to get list medical template!",
        "An error occurred while getting the list of medical templates.",
    )
    MEDICAL_TEMPLATE_GET_LIST_SUCCESS = (
        130008,
        "Get list medical template successfully!",
        "The list of medical templates was retrieved successfully.",
    )
    MEDICAL_TEMPLATE_DELETED_SUCCESS = (
        130009,
        "Medical template deleted successfully!",
        "The medical template has been deleted successfully.",
    )
    MEDICAL_TEMPLATE_UPDATED_SUCCESS = (
        130010,
        "Medical template updated successfully!",
        "The medical template has been updated successfully.",
    )
    MEDICAL_TEMPLATE_CREATED_SUCCESS = (
        130011,
        "Medical template created successfully!",
        "The medical template has been created successfully.",
    )

    # Document
    DOCUMENT_GET_INFO_FAILED = (
        140001,
        _("Get info document failed!", "ドキュメント情報の取得に失敗しました。"),
        "An error occurred while get info document.",
    )
    DOCUMENT_GET_LIST_FAILED = (
        140002,
        _("Get list document failed!", "ドキュメント一覧の取得に失敗しました。"),
        "An error occurred while get list document.",
    )
    DOCUMENT_MANAGEMENT_NOT_FOUND = (
        140003,
        _("Document management not found!", "ドキュメント管理が見つかりません。"),
        "The specified document management does not exist.",
    )
    DOCUMENT_MANAGEMENT_CREATED_FAILED = (
        140004,
        _(
            "Document management creation failed!",
            "ドキュメント管理の作成に失敗しました。",
        ),
        "An error occurred while creating the document management.",
    )
    DOCUMENT_MANAGEMENT_CREATED_SUCCESS = (
        140005,
        _(
            "Document management created successfully!",
            "ドキュメント管理が正常に作成されました。",
        ),
        "The document management has been created successfully.",
    )
    DOCUMENT_MANAGEMENT_UPDATED_FAILED = (
        140006,
        _(
            "Document management update failed!",
            "ドキュメント管理の更新に失敗しました。",
        ),
        "An error occurred while updating the document management.",
    )
    DOCUMENT_MANAGEMENT_UPDATED_SUCCESS = (
        140007,
        _(
            "Document management updated successfully!",
            "ドキュメント管理が正常に更新されました。",
        ),
        "The document management has been updated successfully.",
    )
    DOCUMENT_MANAGEMENT_DELETE_FAILED = (
        140008,
        _(
            "Document management delete failed!",
            "ドキュメント管理の削除に失敗しました。",
        ),
        "An error occurred while deleting the document management.",
    )
    DOCUMENT_MANAGEMENT_DELETE_SUCCESS = (
        140009,
        _(
            "Document management deleted successfully!",
            "ドキュメント管理が正常に削除されました。",
        ),
        "The document management has been deleted successfully.",
    )
    DOCUMENT_MANAGEMENT_INVALID_STATUS = (
        140010,
        _(
            "Invalid document management status!",
            "無効なドキュメント管理ステータスです。",
        ),
        "The specified document management status is invalid.",
    )
    DOCUMENT_MANAGEMENT_GET_PREVIEW_FAILED = (
        140011,
        _(
            "Get preview documents failed!",
            "プレビュードキュメントの取得に失敗しました。",
        ),
        "An error occurred while get preview documents.",
    )
    DOCUMENT_MANAGEMENT_GET_PREVIEW_INVALID_PARAMS = (
        140012,
        _(
            "Either document_id or examination_date is required!",
            "document_id または examination_date が必須です。",
        ),
        "An error occurred while get preview documents.",
    )
    DOCUMENT_MANAGEMENT_ERROR_EMPTY_DOCUMENT_DATA = (
        140013,
        _(
            "Document data must not be empty!",
            "ドキュメントデータを空にすることはできません。",
        ),
        "Field document_data must not be empty.",
    )
    DOCUMENT_MANAGEMENT_ERROR_EMPTY_PREVIEW_DOCUMENT_DATA = (
        140014,
        _(
            "Preview document data must not be empty!",
            "プレビュードキュメントデータを空にすることはできません。",
        ),
        "Field preview_document_data must not be empty.",
    )
    LIBRARY_PATIENT_GET_LIST_FAILED = (
        140015,
        _("Get list library failed!", "ライブラリ一覧の取得に失敗しました。"),
        "An error occurred while get list library.",
    )
    GET_DOCUMENT_VERSION_FAILED = (
        140016,
        _(
            "Get document version failed!",
            "ドキュメントバージョンの取得に失敗しました。",
        ),
        "An error occurred while get document version.",
    )

    # Document Management Version
    DOCUMENT_MANAGEMENT_VERSION_NOT_FOUND = (
        150001,
        "Document management version not found!",
        "The specified document management version does not exist.",
    )

    DOCUMENT_MANAGEMENT_VERSION_CREATED_FAILED = (
        150002,
        "Document management version creation failed!",
        "An error occurred while creating the document management version.",
    )

    DOCUMENT_MANAGEMENT_VERSION_CREATED_SUCCESS = (
        150003,
        "Document management version created successfully!",
        "The document management version has been created successfully.",
    )
    DOCUMENT_MANAGEMENT_NAME_EXISTS = (
        150004,
        "Document management name already exists!",
        "The specified document management name already exists.",
    )

    # Patient Relationship
    PATIENT_RELATIONSHIP_REQUEST_CREATION_FAILED = (
        160001,
        "Patient relationship request creation failed!",
        "An error occurred while creating the patient relationship request.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_ALREADY_EXISTS = (
        160002,
        "Patient relationship request relationship already exists!",
        "The patient relationship request relationship already exists.",
    )
    PATIENT_RELATIONSHIP_REQUEST_APPROVAL_FAILED = (
        160003,
        "Patient relationship request approval failed!",
        "An error occurred while approving the patient relationship request.",
    )
    PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_FAILED = (
        160004,
        "Patient relationship request verification failed!",
        "An error occurred while verifying the patient relationship request.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_NOT_FOUND = (
        160005,
        "Patient relationship request relationship not found!",
        "The patient relationship request relationship does not exist.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_BLOCKED = (
        160006,
        "Patient relationship request relationship request blocked!",
        "The patient relationship request relationship request blocked.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELEASE_FAILED = (
        160007,
        "Patient relationship request release failed!",
        "An error occurred while releasing the patient relationship request.",
    )
    PATIENT_RELATIONSHIP_SUMMARY_GET_FAILED = (
        160008,
        "Patient relationship summary get failed!",
        "An error occurred while get the patient relationship summary.",
    )
    PATIENT_RELATIONSHIP_LIST_GET_FAILED = (
        160009,
        "Patient relationship list get failed!",
        "An error occurred while get the patient relationship list.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_ALREADY_SUCCESS = (
        160010,
        "Patient relationship request relationship request already success!",
        "The patient relationship request relationship request already success.",
    )
    PATIENT_RELATIONSHIP_REQUEST_REQUEST_COUNT_EXCEEDED = (
        160011,
        "Patient relationship request request count exceeded!",
        "The patient relationship request request count exceeded.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_REJECTED = (
        160012,
        "Patient relationship request relationship request rejected!",
        "The patient relationship request relationship request rejected.",
    )
    PATIENT_RELATIONSHIP_RELEASE_FAILED = (
        160013,
        "Patient relationship release failed!",
        "An error occurred while releasing the patient relationship.",
    )
    PATIENT_RELATIONSHIP_SUMMARY_GET_SUCCESS = (
        160014,
        "Patient relationship summary get success!",
        "The patient relationship summary get success.",
    )
    PATIENT_RELATIONSHIP_REQUEST_CREATION_SUCCESS = (
        160015,
        "Patient relationship request creation success!",
        "The patient relationship request creation success.",
    )
    PATIENT_RELATIONSHIP_REQUEST_APPROVAL_SUCCESS = (
        160016,
        "Patient relationship request approval success!",
        "The patient relationship request approval success.",
    )
    PATIENT_RELATIONSHIP_REQUEST_VERIFICATION_SUCCESS = (
        160017,
        "Patient relationship request verification success!",
        "The patient relationship request verification success.",
    )
    PATIENT_RELATIONSHIP_RELEASE_SUCCESS = (
        160018,
        "Patient relationship release success!",
        "The patient relationship release success.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_PENDING = (
        160019,
        "Patient relationship request relationship request pending!",
        "The patient relationship request relationship request pending.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_APPROVED = (
        160020,
        "Patient relationship request relationship request approved!",
        "The patient relationship request relationship request approved.",
    )
    PATIENT_RELATIONSHIP_REQUEST_REJECT_SUCCESS = (
        160021,
        "Patient relationship request relationship request rejected!",
        "The patient relationship request relationship request rejected.",
    )
    PATIENT_RELATIONSHIP_REQUEST_REJECT_FAILED = (
        160022,
        "Patient relationship request relationship request rejected!",
        "The patient relationship request relationship request rejected.",
    )
    PATIENT_RELATIONSHIP_REQUEST_RELATIONSHIP_REQUEST_DISCONNECTED = (
        160023,
        "Patient relationship request relationship request disconnected!",
        "The patient relationship request relationship request disconnected.",
    )

    # SMS
    VERIFICATION_OTP_MESSAGE = (
        170001,
        "Your verification code is {otp}. It expires in {minutes} minutes.",
        "認証コード: {otp}。{minutes}分後に期限切れになります。",
    )

    # Restore S3 Data
    DOCUMENT_IS_RESTORING = (
        180000,
        "Document is restoring!",
        "Document is restoring!",
    )

    RESTORE_S3_DATA_SUCCESS = (
        180001,
        "Restore S3 Data Success!",
        "Restore S3 Data Success!",
    )
    RESTORE_S3_DATA_FAILED = (
        180002,
        "Restore S3 Data Failed!",
        "Restore S3 Data Failed!",
    )

    PROCESS_UPDATE_RESTORE_STATUS_SUCCESS = (
        180003,
        "Process update restore status success",
        "Process update restore status success",
    )
    PROCESS_UPDATE_RESTORE_STATUS_FAILED = (
        180004,
        "Process update restore status failed",
        "Process update restore status failed",
    )

    PROCESS_DELETE_EXPIRED_RESTORE_STATUS_SUCCESS = (
        180005,
        "Process delete expired restore status success",
        "Process delete expired restore status success",
    )
    PROCESS_DELETE_EXPIRED_RESTORE_STATUS_FAILED = (
        180006,
        "Process delete expired restore status failed",
        "Process delete expired restore status failed",
    )

    # Document Group
    DOCUMENT_GROUP_CREATED_FAILED = (
        190001,
        "Failed to create document group!",
        "An error occurred while creating the document group.",
    )
    DOCUMENT_GROUP_CREATED_SUCCESS = (
        190002,
        "Document group created successfully!",
        "The document group has been created successfully.",
    )
    DOCUMENT_GROUP_GET_FAILED = (
        190003,
        "Failed to get document group!",
        "An error occurred while getting the document group.",
    )
    DOCUMENT_GROUP_GET_SUCCESS = (
        190004,
        "Document group retrieved successfully!",
        "The document group has been retrieved successfully.",
    )
    DOCUMENT_GROUP_UPDATED_FAILED = (
        190005,
        "Failed to update document group!",
        "An error occurred while updating the document group.",
    )
    DOCUMENT_GROUP_UPDATED_SUCCESS = (
        190006,
        "Document group updated successfully!",
        "The document group has been updated successfully.",
    )
    DOCUMENT_GROUP_DELETED_FAILED = (
        190007,
        "Failed to delete document group!",
        "An error occurred while deleting the document group.",
    )
    DOCUMENT_GROUP_DELETED_SUCCESS = (
        190008,
        "Document group deleted successfully!",
        "The document group has been deleted successfully.",
    )
    DOCUMENT_GROUP_GET_LIST_FAILED = (
        190009,
        "Failed to get list document group!",
        "An error occurred while getting the list document group.",
    )
    DOCUMENT_GROUP_GET_LIST_SUCCESS = (
        190010,
        "Document group list retrieved successfully!",
        "The document group list has been retrieved successfully.",
    )
    DOCUMENT_GROUP_EXISTS = (
        190011,
        "Document group already exists!",
        "A document group with the same name already exists.",
    )
    DOCUMENT_GROUP_DELETED = (
        190012,
        "Document group deleted!",
        "This document group has already been deleted and cannot be updated.",
    )
    DOCUMENT_GROUP_IN_USE = (
        190012,
        "Document group in use!",
        "This document group cannot be deleted because it is being used by documents or templates.",
    )

    PROCESS_UPDATE_DOCUMENT_S3_STATUS_SUCCESS = (
        180007,
        "Process update document s3 status success",
        "Process update document s3 status success",
    )
    PROCESS_UPDATE_DOCUMENT_S3_STATUS_FAILED = (
        180008,
        "Process update document s3 status failed",
        "Process update document s3 status failed",
    )
