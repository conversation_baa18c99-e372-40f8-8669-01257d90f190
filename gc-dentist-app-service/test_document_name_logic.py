"""
Standalone test script để kiểm tra logic _check_and_get_document_name
Không cần dependencies phứ<PERSON> tạp, chỉ test logic thuần túy
"""
import re
import asyncio
from unittest.mock import AsyncMock, MagicMock


class MockDocumentStatus:
    """Mock DocumentStatus enum"""
    class ACTIVATED:
        value = 3


async def _check_and_get_document_name(db_session, document_name: str) -> str:
    """
    Copy của method cần test để kiểm tra logic
    """
    # Normalize the document name by stripping whitespace
    normalized_name = document_name.strip()
    
    # Handle empty or whitespace-only names
    if not normalized_name:
        raise ValueError("Document name cannot be empty or contain only whitespace")
    
    # Escape special characters in document name for regex pattern
    escaped_name = re.escape(normalized_name)
    # Pattern to match "name (number)" format with proper validation
    pattern = rf"^{escaped_name} \((\d+)\)$"

    # Mock query execution - sẽ đ<PERSON><PERSON>c override trong tests
    result = await db_session.execute(None)
    existing_names = {row[0] for row in result.fetchall()}

    # If the original name doesn't exist, return it
    if normalized_name not in existing_names:
        return normalized_name

    # Find the highest number in existing names with format "name (number)"
    regex = re.compile(pattern)
    max_number = 0

    for name in existing_names:
        # Check if name matches the pattern "document_name (number)"
        match = regex.match(name)
        if match:
            try:
                number = int(match.group(1))
                # Ensure the number is positive and reasonable (prevent overflow)
                if 0 < number <= 999999:  # Reasonable upper limit
                    max_number = max(max_number, number)
            except (ValueError, OverflowError):
                continue  # Ignore invalid or too large numbers

    # Return the next available number
    return f"{normalized_name} ({max_number + 1})"


async def test_case(test_name: str, document_name: str, existing_names: list, expected: str):
    """Helper function để chạy một test case"""
    print(f"\n🧪 Test: {test_name}")
    print(f"   Input: '{document_name}'")
    print(f"   Existing names: {existing_names}")
    
    # Create mock session
    mock_session = AsyncMock()
    mock_result = MagicMock()
    mock_result.fetchall.return_value = [(name,) for name in existing_names]
    mock_session.execute.return_value = mock_result
    
    try:
        result = await _check_and_get_document_name(mock_session, document_name)
        print(f"   Expected: '{expected}'")
        print(f"   Got: '{result}'")
        
        if result == expected:
            print("   ✅ PASS")
            return True
        else:
            print("   ❌ FAIL")
            return False
    except Exception as e:
        if "expected" in locals() and isinstance(expected, type) and issubclass(expected, Exception):
            print(f"   Expected exception: {expected.__name__}")
            print(f"   Got exception: {type(e).__name__}: {e}")
            if isinstance(e, expected):
                print("   ✅ PASS")
                return True
            else:
                print("   ❌ FAIL")
                return False
        else:
            print(f"   ❌ FAIL - Unexpected exception: {type(e).__name__}: {e}")
            return False


async def run_all_tests():
    """Chạy tất cả test cases"""
    print("🚀 Bắt đầu kiểm tra logic _check_and_get_document_name")
    print("=" * 60)
    
    test_cases = [
        # Basic cases
        ("Tên không tồn tại - trả về tên gốc", "Abc", [], "Abc"),
        ("Tên gốc tồn tại - trả về (1)", "Abc", ["Abc"], "Abc (1)"),
        ("Tên gốc và (1) tồn tại - trả về (2)", "Abc", ["Abc", "Abc (1)"], "Abc (2)"),
        
        # Số không liên tục
        ("Số không liên tục - tìm max và +1", "Abc", ["Abc", "Abc (3)", "Abc (5)"], "Abc (6)"),
        ("Nhiều số không liên tục", "Abc", ["Abc", "Abc (1)", "Abc (3)", "Abc (10)"], "Abc (11)"),
        
        # Khoảng trắng
        ("Tên có khoảng trắng đầu/cuối", "  Abc  ", [], "Abc"),
        ("Tên có khoảng trắng với existing", "  Abc  ", ["Abc"], "Abc (1)"),
        
        # Ký tự đặc biệt
        ("Tên có ký tự đặc biệt", "Test (file).txt", [], "Test (file).txt"),
        ("Tên có ký tự đặc biệt với existing", "Test (file).txt", ["Test (file).txt"], "Test (file).txt (1)"),
        ("Tên có dấu ngoặc vuông", "Report [2024]", ["Report [2024]"], "Report [2024] (1)"),
        
        # Số không hợp lệ
        ("Bỏ qua số không hợp lệ", "Abc", ["Abc", "Abc (1)", "Abc (abc)", "Abc (2)"], "Abc (3)"),
        ("Bỏ qua format không đúng", "Abc", ["Abc", "Abc (1)", "Abc ()", "Abc (2)"], "Abc (3)"),
        
        # Unicode
        ("Tên Unicode", "Tài liệu 测试", ["Tài liệu 测试"], "Tài liệu 测试 (1)"),
        
        # Số lớn
        ("Số lớn hợp lệ", "Abc", ["Abc", "Abc (999999)"], "Abc (1000000)"),
        ("Bỏ qua số quá lớn", "Abc", ["Abc", "Abc (1)", "Abc (9999999)"], "Abc (2)"),
        
        # Complex scenario
        ("Scenario phức tạp", "Report [2024]", [
            "Report [2024]", "Report [2024] (1)", "Report [2024] (invalid)", 
            "Report [2024] (3)", "Report [2024] ()", "Report [2024] (10)"
        ], "Report [2024] (11)"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_name, document_name, existing_names, expected in test_cases:
        if await test_case(test_name, document_name, existing_names, expected):
            passed += 1
    
    # Test exception cases
    print(f"\n🧪 Test: Tên rỗng - raise ValueError")
    try:
        mock_session = AsyncMock()
        await _check_and_get_document_name(mock_session, "")
        print("   ❌ FAIL - Should have raised ValueError")
    except ValueError as e:
        if "empty" in str(e):
            print("   ✅ PASS")
            passed += 1
        else:
            print(f"   ❌ FAIL - Wrong error message: {e}")
    except Exception as e:
        print(f"   ❌ FAIL - Wrong exception type: {type(e).__name__}")
    
    total += 1
    
    print(f"\n🧪 Test: Tên chỉ có khoảng trắng - raise ValueError")
    try:
        mock_session = AsyncMock()
        await _check_and_get_document_name(mock_session, "   ")
        print("   ❌ FAIL - Should have raised ValueError")
    except ValueError as e:
        if "empty" in str(e):
            print("   ✅ PASS")
            passed += 1
        else:
            print(f"   ❌ FAIL - Wrong error message: {e}")
    except Exception as e:
        print(f"   ❌ FAIL - Wrong exception type: {type(e).__name__}")
    
    total += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Kết quả: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Tất cả tests đều PASS! Logic hoạt động chính xác.")
    else:
        print(f"⚠️  {total - passed} tests FAILED. Cần kiểm tra lại logic.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(run_all_tests())
