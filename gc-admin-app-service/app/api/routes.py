from api.health_check import router as health_check_router
from api.v1.master.master_api import router as master_router
from api.v1.plans.plans_api import router as plan_router
from api.v1.pricing.pricing_storages_api import router as pricing_storage_router
from api.v1.storage.usage_storage_api import router as usage_storage_router
from api.v1.system_user_api import router as system_user_router
from api.v1.tenant.tenants_api import router as tenant_router
from api.v1.thumbnail_api import router as thumbnail_router
from core.api_version_router import VersionedAPIRouter

router = VersionedAPIRouter()

router.include_router(master_router, prefix="/master-data", tags=["Master data"])
router.include_router(tenant_router, prefix="/tenants", tags=["Tenants"])
router.include_router(
    thumbnail_router,
    prefix="/thumbnails",
    tags=["Thumbnail"],
)
router.include_router(
    pricing_storage_router,
    prefix="/pricing-storages",
    tags=["Pricing Storages Management"],
)
router.include_router(plan_router, prefix="/plans", tags=["Plans Management"])
router.include_router(system_user_router, prefix="/system-user", tags=["System User"])
router.include_router(
    usage_storage_router,
    prefix="/storage-usage",
    tags=["Storage Usage Management"],
)
router.include_router(health_check_router)
