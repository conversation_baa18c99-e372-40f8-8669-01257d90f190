import io
import json

from PIL import Image


def create_mock_json_file(data: dict) -> tuple[str, io.BytesIO, str]:
    """Helper to create an in-memory JSON file tuple for testing."""
    file_content = json.dumps(data).encode("utf-8")
    return ("data.json", io.BytesIO(file_content), "application/json")


def create_mock_image_file(
    filename: str, content_type: str
) -> tuple[str, io.BytesIO, str]:
    """Helper to create an in-memory image file tuple for testing."""
    return (filename, io.BytesIO(b"fake_image_bytes"), content_type)


def create_dummy_image_bytes(
    width: int = 1024, height: int = 768, fmt: str = "JPEG"
) -> bytes:
    """
    Creates a dummy image of a given size and returns its byte content.
    """
    img = Image.new("RGB", (width, height), color="black")
    buffer = io.BytesIO()
    img.save(buffer, format=fmt)
    return buffer.getvalue()
