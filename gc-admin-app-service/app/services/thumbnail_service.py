import io

from configuration.settings import configuration
from PIL import Image, ImageOps
from pillow_heif import register_heif_opener
from schemas.requests.thumbnail_schemas import CreateThumbnailPayload
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import ThumbnailGenerationLog
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import generate_thumbnail_image_path
from gc_dentist_shared.core.enums.thumbnail_generation_logs import ThumbnailStatus
from gc_dentist_shared.core.logger.config import log

register_heif_opener()


THUMBNAIL_FORMAT = "WEBP"
THUMBNAIL_CONTENT_TYPE = "image/webp"


class ThumbnailService:
    def __init__(self, db_session: AsyncSession, s3_client: S3Client):
        self.db_session = db_session
        self.s3_client = s3_client

    async def generate_thumbnail(
        self,
        obj_request: CreateThumbnailPayload,
    ) -> str:
        log.info(
            f"🚀 Starting thumbnail generation for: {obj_request.original_image_path}"
        )

        # Step 1: Create an initial log record to track the job.
        status = ThumbnailStatus.PROCESSING
        log_record = ThumbnailGenerationLog(
            original_image_path=obj_request.original_image_path,
            status=status,
        )
        async with self.db_session.begin():
            self.db_session.add(log_record)

        thumbnail_image_path = ""
        error_message = ""
        try:
            # Step 1: Download original image content
            original_image_bytes = await self.s3_client.get_object(
                key=obj_request.original_image_path
            )

            # Step 2: Create thumbnail using Pillow
            thumbnail_bytes = self._create_thumbnail_bytes(
                original_image_bytes,
                obj_request.thumbnail_width,
                obj_request.thumbnail_height,
                obj_request.quality,
            )

            # Step 3: Upload the thumbnail to S3
            thumbnail_image_path = generate_thumbnail_image_path(
                configuration=configuration,
                original_image_path=obj_request.original_image_path,
            )
            log.info(f"Uploading thumbnail to: {thumbnail_image_path}")
            await self.s3_client.upload_file(
                file=thumbnail_bytes,
                object_name=thumbnail_image_path,
                content_type=THUMBNAIL_CONTENT_TYPE,
            )

            status = ThumbnailStatus.SUCCESS
            log.info(f"✅ Thumbnail generation successful: {thumbnail_image_path}")

        except Exception as e:
            error_message = str(e)
            log.error(
                f"❌ Thumbnail generation failed for {obj_request.original_image_path}: {error_message}"
            )
            status = ThumbnailStatus.FAILED
        finally:
            # Step 6: Always commit the final state of the log record.
            async with self.db_session.begin():  # noqa: ASYNC102
                log_to_update = await self.db_session.get(  # noqa: ASYNC102
                    ThumbnailGenerationLog, log_record.id
                )
                log_to_update.status = status
                log_to_update.thumbnail_image_path = thumbnail_image_path
                log_to_update.error_message = error_message

        return thumbnail_image_path

    @staticmethod
    def _create_thumbnail_bytes(
        original_image_bytes: bytes,
        width: int,
        height: int,
        quality: int,
        fmt: str = THUMBNAIL_FORMAT,
    ) -> bytes:
        with Image.open(io.BytesIO(original_image_bytes)) as img:
            thumbnail_img = ImageOps.fit(
                img,
                (width, height),
                Image.Resampling.LANCZOS,
            )
            buffer = io.BytesIO()
            thumbnail_img.save(buffer, format=fmt, quality=quality)
            return buffer.getvalue()
