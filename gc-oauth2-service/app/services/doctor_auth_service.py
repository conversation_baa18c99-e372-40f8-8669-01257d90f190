import json
import random
import string
from datetime import datetime, timedelta, timezone

from configuration.context.language_context import get_current_language
from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import (
    DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX,
    DOCTOR_LOGIN_REDIS_PREFIX,
    OTP_BLOCK_TIME_MINUTES,
    OTP_LENGTH,
    OTP_MAX_SEND_ATTEMPTS,
    OTP_SEND_WINDOW_MINUTES,
)
from core.decorators.login_audit import audit_login
from core.messages import CustomMessageCode
from dataclass.login_audit_context import LoginAuditContext
from db.db_connection import CentralDatabase, TenantDatabase
from schemas.auth_schema import (
    DoctorChangePasswordRequest,
    DoctorForgotPasswordResetRequest,
    DoctorLoginOTPRequest,
    DoctorLoginRequest,
    DoctorLoginResponse,
    OAuthTokenResponse,
)
from services.auth_service import OAuth2ClientService
from services.common.func_central_db import (
    get_db_name_by_tenant_uuid,
    get_tenant_clinic_by_clinic_no,
)
from services.role_service import RoleService
from sqlalchemy import delete, select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.orm import Session

from gc_dentist_shared.central_models import (
    MailTemplate,
    OAuth2Client,
    OAuth2Token,
    TenantClinic,
)
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token
from gc_dentist_shared.core.common.synchronous_mail import SyncMailClient
from gc_dentist_shared.core.common.synchronous_redis import SyncRedisCli
from gc_dentist_shared.core.constants import UTF8
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.enums.mail_enum import MailTemplateCategoryEnum, MessageType
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.schemas.email_request_schema import MailRequest
from gc_dentist_shared.tenant_models import DoctorUser


class DoctorAuthService:
    def authenticate_user(
        self, clinic_no: str, obj: DoctorLoginRequest
    ) -> DoctorLoginResponse:
        token = None
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    tenant = get_tenant_clinic_by_clinic_no(
                        central_db_session, clinic_no
                    )
                    self.get_internal_oauth_client(central_db_session, obj.client_id)

                    token = set_current_db_name(tenant.db_name)
                    tenant_uuid = str(tenant.tenant_uuid)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    doctor_user = self.verify_doctor_user_credentials(
                        tenant_db_session=tenant_db_session,
                        username=obj.username,
                        plain_password=obj.password,
                        tenant_uuid=tenant_uuid,
                    )

                    current_time = datetime.now(timezone.utc)
                    if (
                        doctor_user.otp_lock_expires_at
                        and doctor_user.otp_lock_expires_at > current_time
                    ):
                        raise CustomValueError(
                            message_code=CustomMessageCode.OTP_TEMPORARILY_LOCKED.code,
                            message=CustomMessageCode.OTP_TEMPORARILY_LOCKED.title,
                        )

                    self.process_otp_with_rate_limiting(
                        tenant_db_session=tenant_db_session,
                        doctor_user=doctor_user,
                        tenant_uuid=tenant_uuid,
                        current_time=current_time,
                    )

            return DoctorLoginResponse(user_id=doctor_user.id)

        except CustomValueError as e:
            log.error(
                f"❌ Failed to authenticate doctor: {obj.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Failed to authenticate doctor: {obj.username}, error: {str(e)}"
            )
            raise e

        finally:
            if token:
                reset_current_db_name(token)

    @audit_login
    def verify_otp(
        self,
        clinic_no: str,
        obj: DoctorLoginOTPRequest,
        login_audit_context: LoginAuditContext,
    ) -> OAuthTokenResponse:
        """
        Verify the email OTP and generate access/refresh tokens

        :param clinic_no: The clinic no from request header
        :param obj: The verification data containing username and OTP
        :param login_audit_context: The login audit context
        :return: Response with access and refresh tokens
        """
        token_db = None
        try:
            login_audit_context.client_id = obj.client_id
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    tenant = get_tenant_clinic_by_clinic_no(
                        central_db_session, clinic_no
                    )
                    login_audit_context.tenant_uuid = tenant.tenant_uuid
                    client = self.get_internal_oauth_client(
                        central_db_session, obj.client_id
                    )
                    tenant_uuid = str(tenant.tenant_uuid)
                    token_db = set_current_db_name(tenant.db_name)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    doctor_user = self.get_doctor_user(tenant_db_session, obj.user_id)
                    login_audit_context.clinic_doctor_id = doctor_user.id

                    redis = SyncRedisCli.get_instance(configuration)
                    aes_gcm = AesGCMRotation(configuration)
                    otp_key = f"{DOCTOR_LOGIN_REDIS_PREFIX}_{tenant_uuid}_{aes_gcm.sha256_hash(obj.user_id)}"
                    otp_data = redis.get(otp_key)

                    # Case OTP expired or not found -> resend OTP
                    if not otp_data:
                        raise CustomValueError(
                            message=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title,
                            message_code=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code,
                        )

                    # Case OTP incorrect
                    otp_data = json.loads(otp_data)
                    if otp_data["otp"] != obj.otp:
                        raise CustomValueError(
                            message=CustomMessageCode.OTP_INCORRECT.title,
                            message_code=CustomMessageCode.OTP_INCORRECT.code,
                        )

                    role_key_ids = RoleService(
                        tenant_db_session=tenant_db_session
                    ).get_doctor_role_key_ids(doctor_user.id)

                    token = self.create_access_token(
                        internal_client=client,
                        user=doctor_user,
                        tenant_uuid=tenant_uuid,
                        role_key_ids=role_key_ids,
                    )

                    # Delete the OTP key and reset otp_first_send_at
                    doctor_user.otp_first_send_at = None
                    tenant_db_session.add(doctor_user)
                    redis.delete(otp_key)

                    return token

        except CustomValueError as e:
            log.error(
                f"❌ Failed to verify OTP for doctor: {obj.user_id}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Failed to verify OTP for doctor: {obj.user_id}, error: {str(e)}"
            )
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    @audit_login
    def login_doctor_username_password(
        self,
        clinic_no: str,
        obj: DoctorLoginRequest,
        login_audit_context: LoginAuditContext,
    ) -> OAuthTokenResponse:
        token_db = None
        try:
            login_audit_context.client_id = obj.client_id
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    tenant: TenantClinic = get_tenant_clinic_by_clinic_no(
                        central_db_session, clinic_no
                    )
                    login_audit_context.tenant_uuid = tenant.tenant_uuid
                    client = self.get_internal_oauth_client(
                        central_db_session, obj.client_id
                    )

                    token_db = set_current_db_name(tenant.db_name)
                    tenant_uuid = str(tenant.tenant_uuid)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    doctor_user = self.verify_doctor_user_credentials(
                        tenant_db_session=tenant_db_session,
                        username=obj.username,
                        plain_password=obj.password,
                        tenant_uuid=tenant_uuid,
                    )
                    login_audit_context.clinic_doctor_id = doctor_user.id
                    role_key_ids = RoleService(
                        tenant_db_session=tenant_db_session
                    ).get_doctor_role_key_ids(doctor_user.id)

            return self.create_access_token(
                internal_client=client,
                user=doctor_user,
                tenant_uuid=tenant_uuid,
                role_key_ids=role_key_ids,
            )

        except CustomValueError as e:
            log.error(
                f"❌ Failed to authenticate doctor: {obj.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Failed to authenticate doctor: {obj.username}, error: {str(e)}"
            )
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_internal_oauth_client",
    )
    def get_internal_oauth_client(self, central_db_session: Session, client_id: str):
        try:
            result = central_db_session.execute(
                select(OAuth2Client).where(OAuth2Client.client_id == client_id)
            )
            client = result.scalar_one_or_none()

            if not client:
                raise CustomValueError(
                    message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                    message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
                )
            return client

        except CustomValueError as e:
            log.error(f"❌ Oauth client not found with client_id: {client_id}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding oauth client: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_doctor_user",
    )
    def get_doctor_user(self, tenant_db_session: Session, user_id: int) -> DoctorUser:
        try:
            stmt = select(DoctorUser).where(
                DoctorUser.id == user_id, DoctorUser.status.is_(True)
            )
            doctor_user = tenant_db_session.execute(stmt).scalar_one_or_none()
            if not doctor_user:
                raise CustomValueError(
                    message=CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
                )

            return doctor_user

        except CustomValueError as e:
            log.error(f"❌ Doctor user not found for user_id: {user_id}")
            raise e

        except (DBAPIError, OperationalError) as e:
            log.error(
                f"❌ Database error while verifying getting doctor user: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Database error while verifying getting doctor user: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_mail_template",
    )
    def get_mail_template(
        self, template_category: int, template_language: str = "ja"
    ) -> MailTemplate:
        """Get mail template from central database"""
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    result = central_db_session.execute(
                        select(MailTemplate).where(
                            MailTemplate.category == template_category,
                            MailTemplate.language == template_language,
                            MailTemplate.status.is_(True),
                        )
                    )
                    template = result.scalar_one_or_none()
                    if not template:
                        raise CustomValueError(
                            message=CustomMessageCode.MAIL_TEMPLATE_NOT_FOUND.title,
                            message_code=CustomMessageCode.MAIL_TEMPLATE_NOT_FOUND.code,
                        )

                    return template

        except CustomValueError as e:
            log.error(f"❌ Mail template not found for category: {template_category}")
            raise e
        except (DBAPIError, OperationalError) as e:
            log.error(f"❌ Database error while finding mail template: {str(e)}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding mail template: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="verify_doctor_user_credentials",
    )
    def verify_doctor_user_credentials(
        self,
        tenant_db_session: Session,
        username: str,
        plain_password: str,
        tenant_uuid: str,
    ):
        try:
            stmt = select(DoctorUser).where(
                DoctorUser.username == username, DoctorUser.status.is_(True)
            )
            doctor_user = tenant_db_session.execute(stmt).scalar_one_or_none()
            if not doctor_user or not doctor_user.validate_password(
                plain_password=plain_password,
                tenant_uuid=tenant_uuid,
            ):
                raise CustomValueError(
                    message=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
                    message_code=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
                )

            return doctor_user

        except CustomValueError as e:
            log.error(f"❌ Invalid doctor user credentials for username: {username}")
            raise e

        except (DBAPIError, OperationalError) as e:
            log.error(
                f"❌ Database error while verifying doctor user credentials: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Database error while verifying doctor user cretentials: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    def process_otp_with_rate_limiting(
        self,
        tenant_db_session: Session,
        doctor_user: DoctorUser,
        tenant_uuid: str,
        current_time: datetime,
    ):
        try:
            redis = SyncRedisCli.get_instance(configuration)
            aes_gcm = AesGCMRotation(configuration)
            otp_key = f"{DOCTOR_LOGIN_REDIS_PREFIX}_{tenant_uuid}_{aes_gcm.sha256_hash(doctor_user.id)}"
            otp = "".join(random.choices(string.digits, k=OTP_LENGTH))
            key_ttl = OTP_SEND_WINDOW_MINUTES * 60

            # Case first OTP request or previous timeframe expired
            if (
                doctor_user.otp_first_send_at is None
                or (current_time - doctor_user.otp_first_send_at).total_seconds()
                > OTP_SEND_WINDOW_MINUTES * 60
            ):
                doctor_user.otp_first_send_at = current_time
                tenant_db_session.add(doctor_user)

                redis.setex(
                    name=otp_key,
                    time=key_ttl,
                    value=json.dumps(
                        {
                            "otp": otp,
                            "current_attempt": 1,
                        }
                    ),
                )
                self.send_otp_to_mail(
                    doctor_email=doctor_user.username,
                    data={
                        "otp": otp,
                        "expires_in": OTP_SEND_WINDOW_MINUTES,
                        "logo_url": configuration.DEFAULT_MAIL_LOGO_URL,
                    },
                    template_category=MailTemplateCategoryEnum.LOGIN_FOR_DOCTOR.value,
                )

            # Case OTP already sent within timeframe and not yet expired
            else:
                otp_data = redis.get(otp_key)
                if not otp_data:
                    raise CustomValueError(
                        message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                        message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
                    )
                otp_data = json.loads(otp_data)
                new_attempt = otp_data["current_attempt"] + 1

                # If exceeded max attempts, block send new OTP and raise error
                if new_attempt > OTP_MAX_SEND_ATTEMPTS:
                    doctor_user.otp_lock_expires_at = current_time + timedelta(
                        minutes=OTP_BLOCK_TIME_MINUTES
                    )
                    tenant_db_session.add(doctor_user)

                    raise CustomValueError(
                        message_code=CustomMessageCode.OTP_TEMPORARILY_LOCKED.code,
                        message=CustomMessageCode.OTP_TEMPORARILY_LOCKED.title,
                    )

                redis.setex(
                    name=otp_key,
                    time=key_ttl,
                    value=json.dumps(
                        {
                            "otp": otp,
                            "current_attempt": new_attempt,
                        }
                    ),
                )
                self.send_otp_to_mail(
                    doctor_email=doctor_user.username,
                    data={
                        "otp": otp,
                        "expires_in": OTP_SEND_WINDOW_MINUTES,
                        "logo_url": configuration.DEFAULT_MAIL_LOGO_URL,
                    },
                    template_category=MailTemplateCategoryEnum.LOGIN_FOR_DOCTOR.value,
                )

        except Exception as e:
            log.error(
                f"❌ Failed to process OTP for doctor: {doctor_user.username}, error: {str(e)}"
            )
            raise e

    def send_otp_to_mail(
        self,
        doctor_email: str,
        data: dict,
        template_category: int,
    ):
        try:
            language = get_current_language() or "ja"
            template = self.get_mail_template(
                template_category=template_category,
                template_language=language,
            )
            mail_client = SyncMailClient.get_instance(configuration)
            body_content = mail_client.render_body_content_template(
                template=template.content, data_bidding=data
            )

            # Create mail request
            mail_request = MailRequest(
                recipients=[doctor_email],
                subject=template.title,
                body=body_content,
                charset=UTF8,
                data_bidding=data,
                category=template.category,
                subtype=MessageType.HTML,
            )

            result = mail_client.send_message(mail_request)
            if result:
                log.info(f"✅ OTP email sent successfully to doctor: {doctor_email}")
            else:
                raise CustomValueError(
                    message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                    message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
                )

        except Exception as e:
            log.error(
                f"❌ Failed to send email to doctor: {doctor_email}, error: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
            )

    def create_access_token(
        self,
        internal_client: OAuth2Client,
        user: DoctorUser,
        tenant_uuid: str,
        role_key_ids: list[int],
    ) -> OAuthTokenResponse:
        try:
            token = encode_jwt_token(
                configuration.RSA_KEY_MANIFEST.get("current_kid"),
                configuration.JWT_RSA_PRIVATE_KEY,
                configuration.JWT_ALGORITHM,
                configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
                "",
                internal_client,
                user,
                " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
                role_key_ids=role_key_ids,
            )
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    OAuth2ClientService.dynamic_save_token_internal_client_sync(
                        central_db_session=central_db_session,
                        token=token,
                        client_id=internal_client.client_id,
                        clinic_doctor_id=user.id,
                        tenant_uuid=tenant_uuid,
                    )

            return OAuthTokenResponse(
                access_token=token.get("access_token"),
                refresh_token=token.get("refresh_token"),
                token_type=token.get("token_type"),
                expires_in=token.get("expires_in"),
                tenant_uuid=tenant_uuid,
            )

        except CustomValueError as e:
            log.error(
                f"❌ Failed to create token for doctor: {user.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Database error while creating token: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    def forgot_password(self, clinic_no: str, obj: DoctorLoginRequest):
        token_db = None
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    tenant: TenantClinic = get_tenant_clinic_by_clinic_no(
                        central_db_session, clinic_no
                    )
                    self.get_internal_oauth_client(central_db_session, obj.client_id)
                    tenant_uuid = str(tenant.tenant_uuid)
                    token_db = set_current_db_name(tenant.db_name)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    doctor_user: DoctorUser = self.get_doctor_user_by_username(
                        tenant_db_session=tenant_db_session,
                        username=obj.username,
                    )
            self.forgot_password_send_otp(
                doctor_user=doctor_user,
                tenant_uuid=tenant_uuid,
                clinic_no=clinic_no,
            )
        except CustomValueError as e:
            log.error(
                f"❌ CustomValueError forgot password for doctor, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Exception forgot password for doctor, error: {str(e)}")
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_doctor_user_by_username",
    )
    def get_doctor_user_by_username(
        self,
        tenant_db_session: Session,
        username: str,
    ) -> DoctorUser:
        try:
            stmt = select(DoctorUser).where(
                DoctorUser.username == username, DoctorUser.status.is_(True)
            )
            doctor_user = tenant_db_session.execute(stmt).scalar_one_or_none()
            if not doctor_user:
                raise CustomValueError(
                    message=CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
                )

            return doctor_user
        except CustomValueError as e:
            log.error(f"❌ Doctor user not found for username: {username}")
            raise e

        except (DBAPIError, OperationalError) as e:
            log.error(
                f"❌ Database error while verifying getting doctor user: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Exception verifying getting doctor user: {str(e)}")
            raise e

    def forgot_password_send_otp(
        self,
        tenant_uuid: str,
        clinic_no: str,
        doctor_user: DoctorUser,
    ):
        redis = SyncRedisCli.get_instance(configuration)
        aes_gcm = AesGCMRotation(configuration)
        otp_key = (
            f"{DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX}_"
            f"{tenant_uuid}_"
            f"{aes_gcm.sha256_hash(doctor_user.username)}"
        )
        otp = "".join(random.choices(string.digits, k=OTP_LENGTH))
        otp_hash = aes_gcm.sha256_hash(otp)
        username_encrypt = aes_gcm.encrypt_data(doctor_user.username)
        clinic_no_encrypt = aes_gcm.encrypt_data(clinic_no)
        key_ttl = configuration.OTP_SEND_FORGOT_PASSWORD_MINUTES * 60

        redis.setex(
            name=otp_key,
            time=key_ttl,
            value=json.dumps(
                {
                    "otp_hash": otp_hash,
                }
            ),
        )
        url = (
            f"{configuration.OTP_SEND_FORGOT_PASSWORD_PATH}"
            f"?clinic_no={clinic_no_encrypt}&username={username_encrypt}&otp={otp_hash}"
        )
        self.send_otp_to_mail(
            doctor_email=doctor_user.username,
            data={
                "url": url,
                "expires_in": configuration.OTP_SEND_FORGOT_PASSWORD_MINUTES // 60,
            },
            template_category=MailTemplateCategoryEnum.RESET_PASSWORD_FOR_DOCTOR.value,
        )

    def forgot_password_verify_otp(
        self,
        client_id: str,
        clinic_no: str,
        username_encrypt: str,
        otp: str,
    ):
        token_db = None
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    self.get_internal_oauth_client(central_db_session, client_id)
                    aes_gcm = AesGCMRotation(configuration)
                    tenant: TenantClinic = get_tenant_clinic_by_clinic_no(
                        central_db_session, clinic_no
                    )
                    token_db = set_current_db_name(tenant.db_name)
                    tenant_uuid = str(tenant.tenant_uuid)

            username = aes_gcm.decrypt_data(username_encrypt)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    doctor_user: DoctorUser = self.get_doctor_user_by_username(
                        tenant_db_session=tenant_db_session,
                        username=username,
                    )
            redis = SyncRedisCli.get_instance(configuration)

            otp_key = (
                f"{DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX}_"
                f"{tenant_uuid}_"
                f"{aes_gcm.sha256_hash(doctor_user.username)}"
            )
            otp_data = redis.get(otp_key)

            if not otp_data:
                raise CustomValueError(
                    message=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title,
                    message_code=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code,
                )

            otp_data = json.loads(otp_data)
            if otp_data["otp_hash"] != otp:
                raise CustomValueError(
                    message=CustomMessageCode.OTP_INCORRECT.title,
                    message_code=CustomMessageCode.OTP_INCORRECT.code,
                )
        except CustomValueError as e:
            log.error(
                f"❌ CustomValueError validate forgot password OTP for doctor, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Exception validate forgot password OTP for doctor, error: {str(e)}"
            )
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    def forgot_password_reset(
        self,
        clinic_no: str,
        obj: DoctorForgotPasswordResetRequest,
    ):
        token_db = None
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    client = self.get_internal_oauth_client(
                        central_db_session, obj.client_id
                    )
                    aes_gcm = AesGCMRotation(configuration)

                    tenant: TenantClinic = get_tenant_clinic_by_clinic_no(
                        central_db_session, clinic_no
                    )
                    tenant_uuid = str(tenant.tenant_uuid)
                    token_db = set_current_db_name(tenant.db_name)
                    username = aes_gcm.decrypt_data(obj.username)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    doctor_user: DoctorUser = self.get_doctor_user_by_username(
                        tenant_db_session=tenant_db_session,
                        username=username,
                    )
                    redis = SyncRedisCli.get_instance(configuration)

                    otp_key = (
                        f"{DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX}_"
                        f"{tenant_uuid}_"
                        f"{aes_gcm.sha256_hash(username)}"
                    )
                    otp_data = redis.get(otp_key)

                    if not otp_data:
                        raise CustomValueError(
                            message=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title,
                            message_code=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code,
                        )

                    otp_data = json.loads(otp_data)
                    if otp_data["otp_hash"] != obj.otp:
                        raise CustomValueError(
                            message=CustomMessageCode.OTP_INCORRECT.title,
                            message_code=CustomMessageCode.OTP_INCORRECT.code,
                        )

                    self.set_password_doctor(
                        tenant_db_session=tenant_db_session,
                        doctor_user=doctor_user,
                        tenant_uuid=tenant_uuid,
                        password=obj.new_password,
                    )
                    redis.delete(otp_key)

            self.delete_token_when_change_password(
                client_id=client.client_id,
                tenant_uuid=tenant_uuid,
                doctor_id=doctor_user.id,
            )

        except CustomValueError as e:
            log.error(
                f"❌ CustomValueError reset forgot password for doctor, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Exception reset forgot password for doctor, error: {str(e)}")
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="set_password_doctor",
    )
    def set_password_doctor(
        self,
        tenant_db_session: Session,
        doctor_user: DoctorUser,
        tenant_uuid: str,
        password: str,
    ):
        try:
            doctor_user.set_password(
                plain_password=password,
                tenant_uuid=tenant_uuid,
            )
            tenant_db_session.add(doctor_user)
        except Exception as e:
            log.error(f"❌ Exception set password for doctor, error: {str(e)}")
            raise e

    def change_password(
        self,
        tenant_uuid: str,
        doctor_id: int,
        obj: DoctorChangePasswordRequest,
    ):
        db_token = None
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    client: OAuth2Client = self.get_internal_oauth_client(
                        central_db_session, obj.client_id
                    )
                    db_name = get_db_name_by_tenant_uuid(
                        central_db_session, tenant_uuid
                    )
                    db_token = set_current_db_name(db_name)

            with TenantDatabase.get_sync_db_session_instance() as tenant_db_session:
                with tenant_db_session.begin():
                    stmt = select(DoctorUser).where(
                        DoctorUser.id == doctor_id, DoctorUser.status.is_(True)
                    )
                    doctor_user = tenant_db_session.execute(stmt).scalar_one_or_none()
                    if not doctor_user:
                        raise CustomValueError(
                            message=CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
                            message_code=CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
                        )
                    if not doctor_user.validate_password(
                        plain_password=obj.current_password,
                        tenant_uuid=tenant_uuid,
                    ):
                        raise CustomValueError(
                            message=CustomMessageCode.INVALID_CURRENT_PASSWORD.title,
                            message_code=CustomMessageCode.INVALID_CURRENT_PASSWORD.code,
                        )
                    doctor_user.set_password(
                        plain_password=obj.new_password,
                        tenant_uuid=tenant_uuid,
                    )
                    tenant_db_session.add(doctor_user)

            self.delete_token_when_change_password(
                client_id=client.client_id,
                tenant_uuid=tenant_uuid,
                doctor_id=doctor_id,
            )
        except CustomValueError as e:
            log.error(
                f"❌ CustomValueError change password for doctor, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Exception change password for doctor, error: {str(e)}")
            raise e

        finally:
            if db_token:
                reset_current_db_name(db_token)

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="delete_token_when_change_password",
    )
    def delete_token_when_change_password(
        self,
        client_id: str,
        doctor_id: int,
        tenant_uuid: str,
    ):

        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    delete_stmt = delete(OAuth2Token).where(
                        OAuth2Token.client_id == client_id,
                        OAuth2Token.tenant_uuid == tenant_uuid,
                        OAuth2Token.clinic_doctor_id == doctor_id,
                    )
                    central_db_session.execute(delete_stmt)
        except Exception as e:
            log.error(f"❌ Exception logout for doctor, error: {str(e)}")
