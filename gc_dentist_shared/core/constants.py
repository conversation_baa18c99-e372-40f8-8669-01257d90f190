from enum import StrEnum

from gc_dentist_shared.core.enums.document import DocumentGroupKeyName
from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType

TWILIO_SMS_CHANNEL = "sms"
TWILIO_STATUS_APPROVED = "approved"

FIELDS_ENCRYPTED = [
    "date_of_birth",
    "address_1",
    "address_2",
    "address_3",
    "phone",
    "email",
]

MAPPING_LANG_REGION = {
    "ja": "ja-JP",
    "en": "en-US",
}

X_TENANT_CLINIC_NO = "X-Tenant-Clinic-No"
X_TENANT_UUID = "X-Tenant-UUID"

AUTHORIZATION_HEADER = "Authorization"

PARAGRAPH_WITH_BREAK_OR_END_REGEX = r"<p[^>]*>(.*?)(<br>|</p>)"
REMOVE_HTML_TAGS_REGEX = r"<[^>]+>"
ISSUER = "https://noda.com"

TIMEZONE_DEFAULT = "Asia/Tokyo"

UTF8 = "UTF-8"

BYTES_PER_BYTE = 1
BYTES_PER_KILOBYTE = 1024  # 1 KB = 1,024 bytes
BYTES_PER_MEGABYTE = 1024**2  # 1 MB = 1,048,576 bytes
BYTES_PER_GIGABYTE = 1024**3  # 1 GB = 1,073,741,824 bytes
BYTES_PER_TERABYTE = 1024**4  # 1 TB = 1,099,511,627,776 bytes


class StorageRedis(StrEnum):
    STORAGE_CURRENT_USAGE = "noda:storage:usage:%s"  # %s is tenant_uuid
    STORAGE_LIMIT = "noda:storage:limit:%s"  # %s is tenant_uuid


class LambdaXRequestValue(StrEnum):
    LAMBDA_CREATE_THUMBNAIL = "lambda-create-thumbnail"
    LAMBDA_IMPORT_TREATMENT_IMAGE_PATIENT = "lambda-import-treatment-image-patient"
    LAMBDA_UPDATE_RESTORED_STATUS = "lambda-update-restored-status"
    LAMBDA_REMOVE_EXPIRED_RESTORED_DATA = "lambda-remove-expired-restored-data"
    LAMBDA_UPDATE_DOCUMENT_S3_STATUS = "lambda-update-document-s3-status"
    LAMBDA_SYNC_STORAGE_USAGE = "lambda-sync-storage-usage"


MAPPING_MEDICAL_DEVICE_TYPE_AND_DOCUMENT_GROUP_KEY = {
    MedicalDeviceType.BFA.value: DocumentGroupKeyName.BITE_FORCE_ANALYZER.value,
    MedicalDeviceType.EMG.value: DocumentGroupKeyName.WEARABLE_ELECTROMYOGRAPH.value,
    MedicalDeviceType.MVT.value: DocumentGroupKeyName.MOTION_TRAINER.value,
    MedicalDeviceType.BTE.value: DocumentGroupKeyName.BITE_EYE.value,
}
