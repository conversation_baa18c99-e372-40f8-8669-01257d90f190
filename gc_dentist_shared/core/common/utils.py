import os
import random
import re
import string
import uuid
from datetime import date, datetime, time, timezone
from typing import Any, Optional
from zoneinfo import ZoneInfo

from pydantic import (
    EmailStr,
    GetCoreSchemaHandler,
    GetJsonSchemaHandler,
    ValidationInfo,
    validate_email,
)
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import PydanticCustomError, core_schema
from pydantic_extra_types.phone_numbers import PhoneNumber

from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.constants import (
    BYTES_PER_BYTE,
    BYTES_PER_GIGABYTE,
    BYTES_PER_KILOBYTE,
    BYTES_PER_MEGABYTE,
    BYTES_PER_TERABYTE,
    TIMEZONE_DEFAULT,
)
from gc_dentist_shared.core.enums.s3_enums import S3Folder
from gc_dentist_shared.core.enums.storage import StorageUnit
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.messages import CustomMessageCode


def split_array(arr: list, max_len: int) -> list[list[Any]]:
    return [arr[i : i + max_len] for i in range(0, len(arr), max_len)]


class ValidateDateString(date):
    """
    Custom date type that accepts a string in the format 'YYYY-MM-DD' or 'YYYY/MM/DD'.
    It can also accept a date object and will return a DateString instance.
    If the input is not a valid date string or date object, it raises a ValueError.

    Usage:
    >>> DateString("2025-01-01")
    DateString(2025, 1, 1)
    >>> DateString("2025/01/01")
    DateString(2025, 1, 1)
    >>> DateString(date(2025, 1, 1))
    DateString(2025, 1, 1)
    """

    DATE_REGEX = re.compile(r"^\d{4}[-/]\d{1,2}[-/]\d{1,2}$")

    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        return core_schema.no_info_plain_validator_function(cls.validate)

    @classmethod
    def validate(cls, value: Any) -> "ValidateDateString":
        if isinstance(value, date):
            return cls(value.year, value.month, value.day)

        if not isinstance(value, str):
            raise ValueError(CustomMessageCode.INVALID_DATE_STRING_TYPE.title)

        if not cls.DATE_REGEX.match(value):
            raise ValueError(CustomMessageCode.INVALID_DATE_STRING_TYPE.title)

        parts = re.split(r"[-/]", value)
        try:
            return cls(int(parts[0]), int(parts[1]), int(parts[2]))
        except Exception:
            raise ValueError(CustomMessageCode.INVALID_DATE_STRING_TYPE.title)

    @classmethod
    def __get_pydantic_json_schema__(
        cls, core_schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        return {
            "type": "string",
            "example": "2025-01-01",
            "pattern": "YYYY-MM-DD | YYYY/MM/DD",
        }


def generate_random_word(length):
    characters = string.ascii_letters + string.digits + "!@#%^&*()-_=+[]{};:,.<>?"
    random_word = "".join(random.choice(characters) for _ in range(length))
    return random_word


class CustomEmailStr(EmailStr):
    @classmethod
    def _validate(cls, __input_value: str) -> str:
        return None if __input_value == "" else validate_email(__input_value)[1]


class CustomPhoneNumber(PhoneNumber):
    default_region_code = "JP"

    @classmethod
    def _validate(cls, phone_number: str, _: ValidationInfo) -> str:
        if not (len(phone_number) >= 10 and len(phone_number) <= 11):
            raise PydanticCustomError(
                "phone_number_length", "Phone number must be 10 or 11 digits"
            )

        try:
            _ = format_phone_number(phone=phone_number)
        except (PydanticCustomError, Exception) as exc:
            raise exc
        return phone_number


class PhoneNumberExistCountryCode(PhoneNumber):
    """
    Custom phone number type that validates phone numbers with an existing country code.
    Example:
    >>> PhoneNumberExistCountryCode("+819012345678")
    """

    default_region_code = "JP"

    @classmethod
    def _validate(cls, phone_number: str, _: ValidationInfo) -> str:
        try:
            _ = format_phone_number(phone=phone_number)
        except (PydanticCustomError, Exception) as exc:
            raise exc
        return phone_number


def generate_password() -> str:
    """Generate a random password"""
    return "".join(random.choices(string.ascii_letters + string.digits, k=8))


def is_katakana(text: str) -> bool:
    katakana_all_pattern = re.compile(r"^[\u30A0-\u30FF\uFF65-\uFF9Fー]+$")
    return bool(katakana_all_pattern.fullmatch(text))


def convert_datetime_with_timezone(
    dt: datetime, time_zone: str = "Asia/Tokyo"
) -> datetime:
    """
    Convert a datetime object to UTC timezone.
    """
    if not isinstance(dt, datetime):
        raise ValueError(CustomMessageCode.INVALID_DATETIME_TYPE.title)
    if dt.tzinfo is not None:
        return dt.astimezone(timezone.utc)

    local_dt = dt.replace(tzinfo=ZoneInfo(time_zone))
    return local_dt.astimezone(timezone.utc)


def convert_datetime_to_utc_specify_timezone(
    input: str,
    fmt: str = "%Y-%m-%d %H:%M:%S",
    timezone_str: str = TIMEZONE_DEFAULT,
) -> datetime:
    local_zone = ZoneInfo(timezone_str)
    dt_local = datetime.strptime(input, fmt).replace(tzinfo=local_zone)

    return dt_local.astimezone(timezone.utc)


def get_date_or_now(target_date: Optional[date] = None) -> date:
    """
    Returns the given date or today's date in UTC if None is provided.
    """
    return target_date or datetime.now(timezone.utc).date()


def calculate_age(birth_date: date) -> int:
    """
    Calculate age based on the birth date.
    :param birth_date: Birth date as a date object.
    :return: Age as an integer.
    """
    today = convert_datetime_with_timezone(datetime.now().astimezone()).date()
    age = (
        today.year
        - birth_date.year
        - ((today.month, today.day) < (birth_date.month, birth_date.day))
    )
    return age


class UUIDString(str):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        return core_schema.no_info_plain_validator_function(cls.validate)

    @classmethod
    def validate(cls, value: str) -> str:
        try:
            uuid.UUID(value)
        except Exception as ex:
            log.error(f"❌ Error invalid uuid: {ex}")
            raise ValueError(CustomMessageCode.INVALID_UUID_STRING.title)

        return value


def is_valid_uuid(value: str, *, version: int = 4) -> bool:
    """Return True if value is a valid UUID string, optionally matching a specific version."""
    if not isinstance(value, str) or not value:
        return False
    try:
        parsed = uuid.UUID(value)
    except (ValueError, AttributeError, TypeError):
        return False

    if version is not None and parsed.version != version:
        return False
    return True


def serializer_for_json(data: dict):
    def convert(v):
        if isinstance(v, (date, datetime, time)):
            return v.isoformat()
        if isinstance(v, uuid.UUID):
            return str(v)
        if isinstance(v, dict):
            return {k: convert(val) for k, val in v.items()}
        if isinstance(v, list):
            return [convert(val) for val in v]
        return v

    return convert(data)


def convert_size(value: float, from_unit: str, to_unit: str) -> float:
    """
    Convert between storage size units (B, KB, MB, GB, TB).
    """
    unit_factors = {
        StorageUnit.BYTE.value: BYTES_PER_BYTE,
        StorageUnit.KILOBYTE.value: BYTES_PER_KILOBYTE,
        StorageUnit.MEGABYTE.value: BYTES_PER_MEGABYTE,
        StorageUnit.GIGABYTE.value: BYTES_PER_GIGABYTE,
        StorageUnit.TERABYTE.value: BYTES_PER_TERABYTE,
    }

    if value is None or value < 0:
        raise ValueError(CustomMessageCode.NEGATIVE_FILE_SIZE.title)

    # Validate units
    if from_unit not in unit_factors:
        raise ValueError(f"{CustomMessageCode.INVALID_STORAGE_UNIT.title}: {from_unit}")
    if to_unit not in unit_factors:
        raise ValueError(f"{CustomMessageCode.INVALID_STORAGE_UNIT.title}: {to_unit}")

    bytes_value = value * unit_factors[from_unit]
    result = bytes_value / unit_factors[to_unit]
    return int(result) if to_unit == StorageUnit.BYTE.value else result


def gigabyte_to_bytes(data_input: float) -> float:
    if data_input == 0:
        return data_input
    return convert_size(data_input, StorageUnit.GIGABYTE.value, StorageUnit.BYTE.value)


def bytes_to_gigabyte(data_input: int) -> float:
    if data_input == 0:
        return data_input
    return convert_size(data_input, StorageUnit.BYTE.value, StorageUnit.GIGABYTE.value)


ALLOW_EXT = {
    "json",
    "csv",
    "pdf",
    "xlsx",
    "jpg",
    "jpeg",
    "png",
    "gif",
    "heic",
    "heif",
    "webp",
    "avif",
    "svg",
}

SAFE_CHARS_PATTERN = re.compile(r"[^\w. ]", re.UNICODE)


def sanitize_filename(full_name_file: str, max_len: int = 120) -> dict:
    """
    input: "file @123.heic"
    output: {
        "file_name": file__123,
        "ext": heic,
    }
    """
    base = os.path.basename(full_name_file)

    # Require an extension
    if "." not in base:
        raise CustomValueError(
            message_code=CustomMessageCode.INVALID_FILE_EXTENSION.code,
            message=CustomMessageCode.INVALID_FILE_EXTENSION.title,
        )

    file_name, ext = base.rsplit(".", 1)
    ext = ext.lower()

    # Validate extension
    if ext not in ALLOW_EXT:
        raise CustomValueError(
            message_code=CustomMessageCode.INVALID_FILE_EXTENSION.code,
            message=CustomMessageCode.INVALID_FILE_EXTENSION.title,
        )

    file_name = file_name.lower()
    file_name = file_name.replace(" ", "_")
    file_name = SAFE_CHARS_PATTERN.sub("_", file_name)
    file_name = file_name.strip(".-")
    if not file_name:
        raise CustomValueError(
            message_code=CustomMessageCode.INVALID_FILE_EXTENSION.code,
            message=CustomMessageCode.INVALID_FILE_EXTENSION.title,
        )

    if len(file_name) > max_len:
        raise CustomValueError(
            message_code=CustomMessageCode.INVALID_LENGTH_FILE_NAME.code,
            message=CustomMessageCode.INVALID_LENGTH_FILE_NAME.title,
        )

    return {
        "file_name": file_name,
        "ext": ext,
    }


INVALID_CHARS_PATTERN = re.compile(r"[^A-Za-z0-9-]")


def sanitize_string(
    original_string: str, max_len: int = 120, lowercase: bool = True
) -> str:
    """
    input: "  014571581-999"
    output: "014571581_999"

    input: "  !@14571581-999"
    output: "14571581_999"
    """
    try:
        original_string = original_string.strip()

        if lowercase:
            original_string = original_string.lower()

        sanitized = INVALID_CHARS_PATTERN.sub("", original_string)
        sanitized = sanitized.replace("-", "_")
        if len(sanitized) > max_len:
            sanitized = sanitized[:max_len]

        return sanitized
    except Exception as ex:
        log.error(f"❌ Error sanitize_string: {ex}")
        return ""


def generate_thumbnail_image_path(configuration, original_image_path: str) -> str:
    parts = original_image_path.split("/")
    try:
        idx = parts.index(S3Folder.MAIN)
        parts[idx] = S3Folder.SUB
    except ValueError:
        parts = [configuration.S3_FOLDER_NAME, S3Folder.SUB, *parts]
    return "/".join(parts)
