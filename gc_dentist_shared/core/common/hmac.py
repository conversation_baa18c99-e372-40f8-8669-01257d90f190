import hashlib
import hmac

from fastapi import Header, HTTPException, Request


def generate_hmac_signature(message: str, secret: str) -> str:
    """Generate HMAC SHA256"""
    return hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()


def verify_hmac_signature(signature: str, message: str, configuration) -> bool:
    """Verify HMAC"""
    expected_signature = generate_hmac_signature(
        message, configuration.MIDDLEWARE_SECRET_KEY
    )
    return hmac.compare_digest(expected_signature, signature)


def hmac_authentication(
    configuration, x_timestamp: str = Header(None), x_signature: str = Header(None)
):
    """authentication HMAC"""
    if not x_timestamp or not x_signature:
        raise HTTPException(status_code=401, detail="Missing authentication headers")

    message = x_timestamp
    if not verify_hmac_signature(x_signature, message, configuration):
        raise HTTPException(status_code=401, detail="Invalid HMAC signature")


def depends_hmac_authentication(configuration):
    """Dependency for HMAC authentication"""

    def hmac_authentication(
        x_timestamp: str = Header(None), x_signature: str = Header(None)
    ):
        if not x_timestamp or not x_signature:
            raise HTTPException(
                status_code=401, detail="Missing authentication headers"
            )

        message = x_timestamp
        if not verify_hmac_signature(x_signature, message, configuration):
            raise HTTPException(status_code=401, detail="Invalid HMAC signature")

    return hmac_authentication


def hmac_authentication_dynamic_key(secret_key: str):
    def verify_hmac_signature(request: Request):
        client_hmac = request.headers.get("X-Signature")
        x_timestamp = request.headers.get("X-Timestamp")
        if not client_hmac:
            raise HTTPException(status_code=401, detail="HMAC signature missing")

        if not x_timestamp:
            raise HTTPException(status_code=401, detail="Timestamp header missing")

        expected_signature = generate_hmac_signature(
            str(x_timestamp), secret_key.encode()
        )
        if not hmac.compare_digest(client_hmac, expected_signature):
            raise HTTPException(status_code=401, detail="Invalid HMAC signature")

    return verify_hmac_signature
