from configuration.settings import configuration
from fastapi import Depends, HTTPException, Request, status

from gc_dentist_shared.core.common.hmac import hmac_authentication
from gc_dentist_shared.core.logger.config import log


class APIMiddlewareVerifyKey:
    def __init__(self, secret_key_setting: str | None = None):
        self.secret_key_setting = (
            secret_key_setting or configuration.MIDDLEWARE_SECRET_KEY
        )

    async def __call__(self, request: Request):
        try:
            x_signature = request.headers.get("X-Signature")
            x_timestamp = request.headers.get("X-Timestamp")

            if not x_signature or not x_timestamp:
                log.error("❌ Missing API authentication headers.")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Missing API authentication headers.",
                )

            # This will raise HTTPException with 401 if validation fails
            hmac_authentication(configuration, x_timestamp, x_signature)

            return request
        except HTTPException as e:
            raise e
        except Exception as e:
            log.error(f"❌ api_verify_key error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="api_verify_key Error.",
            )


def DependsAPIMiddlewareVerifyKey(secret_key: str | None = None):
    return Depends(APIMiddlewareVerifyKey(secret_key_setting=secret_key))
